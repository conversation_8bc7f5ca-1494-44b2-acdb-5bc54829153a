import { create } from 'zustand';
import { User } from '@supabase/supabase-js';
import { type Database } from '@/lib/utils/supabase/database.types';

// Define types for Restaurant and Employee
type Restaurant = Database['public']['Tables']['restaurants']['Row'];

interface AppState {
  user: User | null;
  setUser: (user: User | null) => void;
  userRestaurants: Restaurant[]; // List of restaurants owned by the current user
  setUserRestaurants: (restaurants: Restaurant[]) => void;
  selectedRestaurantId: string | null; // The ID of the currently active restaurant
  setSelectedRestaurantId: (id: string | null) => void;
  isAppInitialized: boolean; // To track if initial data (user, restaurants) has been loaded
  setAppInitialized: (initialized: boolean) => void;
}

export const useAppStore = create<AppState>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  userRestaurants: [],
  setUserRestaurants: (restaurants) => set({ userRestaurants: restaurants }),
  selectedRestaurantId: null,
  setSelectedRestaurantId: (id) => set({ selectedRestaurantId: id }),
  isAppInitialized: false,
  setAppInitialized: (initialized) => set({ isAppInitialized: initialized }),
}));

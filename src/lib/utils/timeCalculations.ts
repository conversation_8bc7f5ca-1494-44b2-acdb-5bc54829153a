// Define a type for a single break object, matching the shift_breaks table structure
export type ShiftBreak = {
  id?: string; // Optional for new breaks
  break_start_time: Date | string;
  break_end_time: Date | string;
  is_paid_break: boolean;
};

/**
 * Calculates the total hours worked based on shift start and end times,
 * and accounts for multiple break times (paid or unpaid).
 *
 * @param shiftStartTime The start time of the shift (Date object or ISO string).
 * @param shiftEndTime The end time of the shift (Date object or ISO string).
 * @param breaks An array of ShiftBreak objects.
 * @param referenceDateForBreaks A date string (YYYY-MM-DD) to combine with break time strings for accurate Date object creation.
 * @returns The total hours worked as a number, or null if times are invalid.
 */
export function calculateHoursWorked(
  shiftStartTime: Date | string,
  shiftEndTime: Date | string,
  breaks: ShiftBreak[] = [],
  referenceDateForBreaks: string // Added parameter
): number | null {
  const start = new Date(shiftStartTime);
  const end = new Date(shiftEndTime);

  // Basic validation for valid shift dates
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    console.error(
      'Invalid shift start or end time provided for hours calculation.'
    );
    return null;
  }

  // Ensure shift end time is not before shift start time
  if (end.getTime() < start.getTime()) {
    console.warn(
      'Shift end time is before shift start time. Returning 0 hours.'
    );
    return 0;
  }

  // Calculate total shift duration in milliseconds
  const totalShiftDurationMs = end.getTime() - start.getTime();
  let effectiveDurationMs = totalShiftDurationMs;

  // Iterate through each break and subtract unpaid break time
  breaks.forEach((b) => {
    if (!b.is_paid_break) {
      // Only subtract if it's an unpaid break
      // Combine the referenceDateForBreaks with the break time string
      const breakStart = combineDateTime(
        referenceDateForBreaks,
        b.break_start_time as string
      );
      const breakEnd = combineDateTime(
        referenceDateForBreaks,
        b.break_end_time as string
      );

      if (breakStart && breakEnd && breakEnd.getTime() > breakStart.getTime()) {
        const breakDurationMs = breakEnd.getTime() - breakStart.getTime();
        effectiveDurationMs -= breakDurationMs;
      } else {
        console.warn(
          'Invalid break start or end time, or break end is before break start for one of the breaks. Break not subtracted.'
        );
      }
    }
  });

  // Convert effective duration from milliseconds to hours
  const effectiveHours = effectiveDurationMs / (1000 * 60 * 60);

  // Ensure hours worked is not negative
  return Math.max(0, effectiveHours);
}

/**
 * Calculates the duration of a single break in minutes.
 * @param breakStartTime The start time of the break (Date object or ISO string).
 * @param breakEndTime The end time of the break (Date object or ISO string).
 * @param referenceDateForBreaks A date string (YYYY-MM-DD) to combine with break time strings.
 * @returns The duration of the break in minutes, or 0 if times are invalid.
 */
export function calculateBreakDurationMinutes(
  breakStartTime: Date | string,
  breakEndTime: Date | string,
  referenceDateForBreaks: string // Added parameter
): number {
  // Combine the referenceDateForBreaks with the break time string
  const start = combineDateTime(
    referenceDateForBreaks,
    breakStartTime as string
  );
  const end = combineDateTime(referenceDateForBreaks, breakEndTime as string);

  if (
    !start ||
    !end ||
    isNaN(start.getTime()) ||
    isNaN(end.getTime()) ||
    end.getTime() < start.getTime()
  ) {
    return 0;
  }

  const durationMs = end.getTime() - start.getTime();
  return durationMs / (1000 * 60); // Convert milliseconds to minutes
}

/**
 * Calculates the total duration of all breaks in minutes.
 * @param breaks An array of ShiftBreak objects.
 * @param referenceDateForBreaks A date string (YYYY-MM-DD) to combine with break time strings.
 * @returns The total duration of all breaks in minutes.
 */
export function calculateTotalBreakMinutes(
  breaks: ShiftBreak[],
  referenceDateForBreaks: string // Added parameter
): number {
  return breaks.reduce((total, b) => {
    return (
      total +
      calculateBreakDurationMinutes(
        b.break_start_time,
        b.break_end_time,
        referenceDateForBreaks
      )
    );
  }, 0);
}

/**
 * Formats a Date object or ISO string into a time string (e.g., "HH:MM").
 * @param dateInput The date to format.
 * @returns Formatted time string or empty string if invalid.
 */
export function formatTime(dateInput: Date | string | null): string {
  if (!dateInput) return '';
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) return '';
  return date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  });
}

/**
 * Formats a Date object or ISO string into a date string (e.g., "YYYY-MM-DD").
 * @param dateInput The date to format.
 * @returns Formatted date string or empty string if invalid.
 */
export function formatDate(dateInput: Date | string | null): string {
  if (!dateInput) return '';
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) return '';
  return date.toISOString().split('T')[0];
}

/**
 * Combines a date string (YYYY-MM-DD) and a time string (HH:MM) into a Date object.
 * @param dateString Date in 'YYYY-MM-DD' format.
 * @param timeString Time in 'HH:MM' format.
 * @returns A Date object, or null if inputs are invalid.
 */
export function combineDateTime(
  dateString: string,
  timeString: string
): Date | null {
  if (!dateString || !timeString) return null;
  const combinedString = `${dateString}T${timeString}:00`; // Add seconds for ISO format
  const date = new Date(combinedString);
  return isNaN(date.getTime()) ? null : date;
}

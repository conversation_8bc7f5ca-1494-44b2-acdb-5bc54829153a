import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { calculateHoursWorked, ShiftBreak } from '@/lib/utils/timeCalculations';

// --- Type Definitions ---
type Shift = Database['public']['Tables']['shifts']['Row'];
type Employee = Database['public']['Tables']['employees']['Row'];
type Role = Database['public']['Tables']['roles']['Row'];
type ExistingShiftBreak = Database['public']['Tables']['shift_breaks']['Row'];

// Extended Shift type to include joined employee, role, and breaks data
export type ShiftWithDetails = Shift & {
  employees: {
    id: string;
    first_name: string;
    last_name: string;
    is_active: boolean;
  } | null;
  roles: { id: string; role_name: string; tip_share_weight: number } | null;
  shift_breaks: ExistingShiftBreak[];
};

// Type for the result of tip distribution
export type TipDistributionResult = {
  employee_id: string;
  employee_name: string;
  role_name: string;
  hours_worked: number;
  weighted_hours?: number; // Optional, used in points_system
  amount_distributed: number;
};

// Type for role-based percentage configuration
export type RolePercentageConfig = {
  [roleId: string]: number; // Map of role_id to percentage (e.g., { "uuid-server": 70, "uuid-bartender": 30 })
};

// --- Core Tip Distribution Logic ---

/**
 * Fetches all completed shifts for a given restaurant and date,
 * along with associated employee, role, and break details,
 * filtering for active employees.
 *
 * @param restaurantId The ID of the restaurant.
 * @param poolDate The date for which to fetch shifts (YYYY-MM-DD format).
 * @returns An array of ShiftWithDetails for eligible shifts.
 */
export async function fetchEligibleShiftsForTipPool(
  restaurantId: string,
  poolDate: string
): Promise<ShiftWithDetails[]> {
  const startOfDay = new Date(poolDate);
  startOfDay.setHours(0, 0, 0, 0);
  const endOfDay = new Date(poolDate);
  endOfDay.setHours(23, 59, 59, 999);

  const { data, error } = await supabase
    .from('shifts')
    .select(
      `
      *,
      employees (
        id,
        first_name,
        last_name,
        is_active
      ),
      roles (
        id,
        role_name,
        tip_share_weight
      ),
      shift_breaks (*)
    `
    )
    .eq('restaurant_id', restaurantId)
    .gte('clock_in_time', startOfDay.toISOString()) // Only include clocked-in shifts
    .lte('clock_out_time', endOfDay.toISOString()) // Only include clocked-out shifts
    .not('clock_in_time', 'is', null) // Ensure clock_in_time is not null
    .not('clock_out_time', 'is', null) // Ensure clock_out_time is not null
    .order('planned_start_time', { ascending: true });

  if (error) {
    console.error('Error fetching shifts for tip pool:', error);
    throw new Error(`Failed to fetch shifts for tip pool: ${error.message}`);
  }

  // Filter out shifts for inactive employees or roles with 0 tip_share_weight
  const eligibleShifts = data.filter(
    (shift) =>
      shift.employees?.is_active && (shift.roles?.tip_share_weight ?? 0) > 0
  ) as ShiftWithDetails[]; // Cast to ensure types are correct after filtering

  return eligibleShifts;
}

/**
 * Distributes tips based on the chosen method.
 *
 * @param totalTips The total amount of tips in the pool.
 * @param shifts An array of eligible ShiftWithDetails objects.
 * @param distributionMethod The method to use ('hours_worked', 'points_system', 'role_based_percentage').
 * @param rolePercentages Optional: A map of role_id to percentage for 'role_based_percentage' method.
 * @returns An array of TipDistributionResult for each employee.
 */
export function distributeTips(
  totalTips: number,
  shifts: ShiftWithDetails[],
  distributionMethod:
    | 'hours_worked'
    | 'points_system'
    | 'role_based_percentage',
  rolePercentages?: RolePercentageConfig
): TipDistributionResult[] {
  if (totalTips <= 0 || shifts.length === 0) {
    return []; // No tips to distribute or no eligible shifts
  }

  const employeeContributions: {
    [employeeId: string]: {
      employee_name: string;
      role_name: string; // Store one role name, or consider if an employee can have multiple relevant roles
      total_hours_worked: number;
      total_weighted_hours: number;
    };
  } = {};

  // Aggregate hours and weighted hours for each employee
  shifts.forEach((shift) => {
    const employeeId = shift.employee_id;
    const employeeName = `${shift.employees?.first_name} ${shift.employees?.last_name || ''}`;
    const roleName = shift.roles?.role_name || 'Unknown Role';
    const tipShareWeight = shift.roles?.tip_share_weight ?? 0;

    // Use planned_start_time as reference date for break calculations
    const referenceDate = shift.planned_start_time.split('T')[0];

    const actualHoursWorked = calculateHoursWorked(
      shift.clock_in_time!,
      shift.clock_out_time!,
      shift.shift_breaks.map((b) => ({
        ...b,
        is_paid_break: b.is_paid_break ?? false,
      })),
      referenceDate
    );

    if (actualHoursWorked === null || actualHoursWorked <= 0) {
      return; // Skip shifts with no valid hours worked
    }

    const weightedHours = actualHoursWorked * tipShareWeight;

    if (!employeeContributions[employeeId]) {
      employeeContributions[employeeId] = {
        employee_name: employeeName,
        role_name: roleName, // Simplistic: takes the role from the first shift encountered
        total_hours_worked: 0,
        total_weighted_hours: 0,
      };
    }
    employeeContributions[employeeId].total_hours_worked += actualHoursWorked;
    employeeContributions[employeeId].total_weighted_hours += weightedHours;
  });

  const distributionResults: TipDistributionResult[] = [];

  switch (distributionMethod) {
    case 'hours_worked': {
      let totalEligibleHours = 0;
      Object.values(employeeContributions).forEach((emp) => {
        totalEligibleHours += emp.total_hours_worked;
      });

      if (totalEligibleHours === 0) return []; // Avoid division by zero

      const tipRatePerHour = totalTips / totalEligibleHours;

      for (const empId in employeeContributions) {
        const emp = employeeContributions[empId];
        const amount = emp.total_hours_worked * tipRatePerHour;
        distributionResults.push({
          employee_id: empId,
          employee_name: emp.employee_name,
          role_name: emp.role_name,
          hours_worked: parseFloat(emp.total_hours_worked.toFixed(2)),
          amount_distributed: parseFloat(amount.toFixed(2)),
        });
      }
      break;
    }

    case 'points_system': {
      let totalWeightedHours = 0;
      Object.values(employeeContributions).forEach((emp) => {
        totalWeightedHours += emp.total_weighted_hours;
      });

      if (totalWeightedHours === 0) return []; // Avoid division by zero

      const tipRatePerWeightedHour = totalTips / totalWeightedHours;

      for (const empId in employeeContributions) {
        const emp = employeeContributions[empId];
        const amount = emp.total_weighted_hours * tipRatePerWeightedHour;
        distributionResults.push({
          employee_id: empId,
          employee_name: emp.employee_name,
          role_name: emp.role_name,
          hours_worked: parseFloat(emp.total_hours_worked.toFixed(2)),
          weighted_hours: parseFloat(emp.total_weighted_hours.toFixed(2)),
          amount_distributed: parseFloat(amount.toFixed(2)),
        });
      }
      break;
    }

    case 'role_based_percentage': {
      if (!rolePercentages || Object.keys(rolePercentages).length === 0) {
        console.warn(
          'Role percentages not provided for role_based_percentage distribution.'
        );
        return [];
      }

      // First, group employees by their role_id and calculate total hours per role
      const rolesData: {
        [roleId: string]: {
          total_hours: number;
          employees: {
            employee_id: string;
            employee_name: string;
            hours_worked: number;
            role_name: string;
          }[];
        };
      } = {};

      shifts.forEach((shift) => {
        const roleId = shift.role_id;
        const employeeId = shift.employee_id;
        const employeeName = `${shift.employees?.first_name} ${shift.employees?.last_name || ''}`;
        const roleName = shift.roles?.role_name || 'Unknown Role';

        const referenceDate = shift.planned_start_time.split('T')[0];
        const actualHoursWorked = calculateHoursWorked(
          shift.clock_in_time!,
          shift.clock_out_time!,
          shift.shift_breaks.map((b) => ({
            ...b,
            is_paid_break: b.is_paid_break ?? false,
          })),
          referenceDate
        );

        if (actualHoursWorked === null || actualHoursWorked <= 0) {
          return;
        }

        if (!rolesData[roleId]) {
          rolesData[roleId] = {
            total_hours: 0,
            employees: [],
          };
        }
        rolesData[roleId].total_hours += actualHoursWorked;
        rolesData[roleId].employees.push({
          employee_id: employeeId,
          employee_name: employeeName,
          hours_worked: actualHoursWorked,
          role_name: roleName,
        });
      });

      // Distribute tips based on role percentages
      for (const roleId in rolePercentages) {
        const percentage = rolePercentages[roleId];
        const rolePoolAmount = totalTips * (percentage / 100);
        const roleInfo = rolesData[roleId];

        if (roleInfo && roleInfo.total_hours > 0) {
          const tipRatePerRoleHour = rolePoolAmount / roleInfo.total_hours;
          roleInfo.employees.forEach((emp) => {
            const amount = emp.hours_worked * tipRatePerRoleHour;
            distributionResults.push({
              employee_id: emp.employee_id,
              employee_name: emp.employee_name,
              role_name: emp.role_name,
              hours_worked: parseFloat(emp.hours_worked.toFixed(2)),
              amount_distributed: parseFloat(amount.toFixed(2)),
            });
          });
        }
      }
      break;
    }

    default:
      console.warn(`Unknown distribution method: ${distributionMethod}`);
      break;
  }

  return distributionResults;
}

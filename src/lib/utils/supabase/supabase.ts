// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js';

// Ensure these environment variables are set in your .env.local file
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase URL or Anon Key. Please check your .env.local file.'
  );
}

// Create a single Supabase client for interacting with your database
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// You can also create a client for server-side operations if needed (e.g., in API routes or server components)
// import { createServerClient, type CookieOptions } from '@supabase/ssr'
// import { cookies } from 'next/headers'

// export function createSupabaseServerClient() {
//   const cookieStore = cookies()

//   return createServerClient(
//     process.env.NEXT_PUBLIC_SUPABASE_URL!,
//     process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
//     {
//       cookies: {
//         get(name: string) {
//           return cookieStore.get(name)?.value
//         },
//         set(name: string, value: string, options: CookieOptions) {
//           try {
//             cookieStore.set({ name, value, ...options })
//           } catch (error) {
//             // The `cookies().set()` method can only be called in a Server Context.
//             // We're only using this to set a cookie when logging in.
//             console.warn('Failed to set cookie:', error);
//           }
//         },
//         remove(name: string, options: CookieOptions) {
//           try {
//             cookieStore.set({ name, value: '', ...options })
//           } catch (error) {
//             // The `cookies().set()` method can only be called in a Server Context.
//             // We're only using this to remove a cookie when logging out.
//             console.warn('Failed to remove cookie:', error);
//           }
//         },
//       },
//     }
//   )
// }

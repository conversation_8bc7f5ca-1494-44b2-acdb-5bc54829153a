export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.12 (cd3cf9e)';
  };
  public: {
    Tables: {
      employees: {
        Row: {
          created_at: string | null;
          first_name: string;
          id: string;
          is_active: boolean | null;
          last_name: string;
          position: string | null;
          restaurant_id: string;
        };
        Insert: {
          created_at?: string | null;
          first_name: string;
          id?: string;
          is_active?: boolean | null;
          last_name: string;
          position?: string | null;
          restaurant_id: string;
        };
        Update: {
          created_at?: string | null;
          first_name?: string;
          id?: string;
          is_active?: boolean | null;
          last_name?: string;
          position?: string | null;
          restaurant_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'employees_restaurant_id_fkey';
            columns: ['restaurant_id'];
            isOneToOne: false;
            referencedRelation: 'restaurants';
            referencedColumns: ['id'];
          },
        ];
      };
      restaurants: {
        Row: {
          address: string | null;
          created_at: string | null;
          id: string;
          name: string;
          owner_id: string | null;
          payroll_period_type: string | null;
          payroll_start_day: string | null;
          phone_number: string | null;
        };
        Insert: {
          address?: string | null;
          created_at?: string | null;
          id?: string;
          name: string;
          owner_id?: string | null;
          payroll_period_type?: string | null;
          payroll_start_day?: string | null;
          phone_number?: string | null;
        };
        Update: {
          address?: string | null;
          created_at?: string | null;
          id?: string;
          name?: string;
          owner_id?: string | null;
          payroll_period_type?: string | null;
          payroll_start_day?: string | null;
          phone_number?: string | null;
        };
        Relationships: [];
      };
      roles: {
        Row: {
          default_pay_rate: number;
          id: string;
          role_name: string;
          tip_share_weight: number;
        };
        Insert: {
          default_pay_rate: number;
          id?: string;
          role_name: string;
          tip_share_weight?: number;
        };
        Update: {
          default_pay_rate?: number;
          id?: string;
          role_name?: string;
          tip_share_weight?: number;
        };
        Relationships: [];
      };
      shift_breaks: {
        Row: {
          break_end_time: string;
          break_start_time: string;
          created_at: string | null;
          id: string;
          is_paid_break: boolean | null;
          shift_id: string;
        };
        Insert: {
          break_end_time: string;
          break_start_time: string;
          created_at?: string | null;
          id?: string;
          is_paid_break?: boolean | null;
          shift_id: string;
        };
        Update: {
          break_end_time?: string;
          break_start_time?: string;
          created_at?: string | null;
          id?: string;
          is_paid_break?: boolean | null;
          shift_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'shift_breaks_shift_id_fkey';
            columns: ['shift_id'];
            isOneToOne: false;
            referencedRelation: 'shifts';
            referencedColumns: ['id'];
          },
        ];
      };
      shifts: {
        Row: {
          clock_in_time: string | null;
          clock_out_time: string | null;
          employee_id: string;
          hours_worked: number | null;
          id: string;
          planned_end_time: string;
          planned_start_time: string;
          restaurant_id: string;
          role_id: string;
        };
        Insert: {
          clock_in_time?: string | null;
          clock_out_time?: string | null;
          employee_id: string;
          hours_worked?: number | null;
          id?: string;
          planned_end_time: string;
          planned_start_time: string;
          restaurant_id: string;
          role_id: string;
        };
        Update: {
          clock_in_time?: string | null;
          clock_out_time?: string | null;
          employee_id?: string;
          hours_worked?: number | null;
          id?: string;
          planned_end_time?: string;
          planned_start_time?: string;
          restaurant_id?: string;
          role_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_shifts_role_id';
            columns: ['role_id'];
            isOneToOne: false;
            referencedRelation: 'roles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'shifts_employee_id_fkey';
            columns: ['employee_id'];
            isOneToOne: false;
            referencedRelation: 'employees';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'shifts_restaurant_id_fkey';
            columns: ['restaurant_id'];
            isOneToOne: false;
            referencedRelation: 'restaurants';
            referencedColumns: ['id'];
          },
        ];
      };
      tip_distributions: {
        Row: {
          amount_distributed: number;
          distributed_at: string | null;
          employee_id: string;
          id: string;
          tip_pool_id: string;
        };
        Insert: {
          amount_distributed: number;
          distributed_at?: string | null;
          employee_id: string;
          id?: string;
          tip_pool_id: string;
        };
        Update: {
          amount_distributed?: number;
          distributed_at?: string | null;
          employee_id?: string;
          id?: string;
          tip_pool_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'tip_distributions_employee_id_fkey';
            columns: ['employee_id'];
            isOneToOne: false;
            referencedRelation: 'employees';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'tip_distributions_tip_pool_id_fkey';
            columns: ['tip_pool_id'];
            isOneToOne: false;
            referencedRelation: 'tip_pools';
            referencedColumns: ['id'];
          },
        ];
      };
      tip_pools: {
        Row: {
          distribution_config: Json | null;
          distribution_method: string | null;
          id: string;
          is_distributed: boolean | null;
          pool_date: string;
          restaurant_id: string;
          total_tips: number;
        };
        Insert: {
          distribution_config?: Json | null;
          distribution_method?: string | null;
          id?: string;
          is_distributed?: boolean | null;
          pool_date: string;
          restaurant_id: string;
          total_tips: number;
        };
        Update: {
          distribution_config?: Json | null;
          distribution_method?: string | null;
          id?: string;
          is_distributed?: boolean | null;
          pool_date?: string;
          restaurant_id?: string;
          total_tips?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'tip_pools_restaurant_id_fkey';
            columns: ['restaurant_id'];
            isOneToOne: false;
            referencedRelation: 'restaurants';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  'public'
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {},
  },
} as const;

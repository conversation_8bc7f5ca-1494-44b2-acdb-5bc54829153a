'use client';

import { useState } from 'react';
import { supabase } from '@/lib/utils/supabase/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function AuthPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isLogin, setIsLogin] = useState(true); // Toggle between login and sign up

  const handleAuth = async (type: 'login' | 'signup') => {
    setLoading(true);
    setMessage('');
    try {
      let response;
      if (type === 'signup') {
        response = await supabase.auth.signUp({
          email,
          password,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/callback`, // Redirect after email confirmation
          },
        });
      } else {
        response = await supabase.auth.signInWithPassword({
          email,
          password,
        });
      }

      const { data, error } = response;

      if (error) {
        setMessage(`Error: ${error.message}`);
      } else if (type === 'signup' && data.user === null) {
        // For signup, if user is null, it means email confirmation is required
        setMessage('Check your email for the confirmation link!');
      } else {
        setMessage(
          type === 'login'
            ? 'Logged in successfully!'
            : 'Signed up successfully! Redirecting...'
        );
        // Supabase client will automatically manage session and redirect if successful
        // No explicit redirect needed here as AuthGuard will handle it.
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setMessage(`An unexpected error occurred: ${err.message}`);
      } else {
        setMessage('An unexpected error occurred.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <Card className="w-full max-w-md shadow-lg rounded-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-800">
            {isLogin ? 'Login' : 'Sign Up'}
          </CardTitle>
          <CardDescription className="text-gray-600">
            {isLogin
              ? 'Enter your credentials to access your account'
              : 'Create a new account'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email
            </label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
            />
          </div>
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Password
            </label>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
            />
          </div>

          <Button
            onClick={() => handleAuth(isLogin ? 'login' : 'signup')}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
          >
            {loading ? 'Loading...' : isLogin ? 'Login' : 'Sign Up'}
          </Button>

          {message && (
            <p
              className={`text-center text-sm ${message.includes('Error') ? 'text-red-500' : 'text-green-600'}`}
            >
              {message}
            </p>
          )}

          <div className="text-center text-sm text-gray-600 mt-4">
            {isLogin ? (
              <>
                Do not have an account?{' '}
                <button
                  onClick={() => setIsLogin(false)}
                  className="text-blue-600 hover:underline focus:outline-none"
                >
                  Sign Up
                </button>
              </>
            ) : (
              <>
                Already have an account?{' '}
                <button
                  onClick={() => setIsLogin(true)}
                  className="text-blue-600 hover:underline focus:outline-none"
                >
                  Login
                </button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

'use client'; // This directive indicates that this is a Client Component

import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase'; // Import your Supabase client
import { type Database } from '@/lib/utils/supabase/database.types'; // Assuming you've generated types for your Supabase schema
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'; // Shadcn/ui Card component
import { Skeleton } from '@/components/ui/skeleton'; // Shadcn/ui Skeleton component for loading state

// Define a type for your restaurant data for better type safety
type Restaurant = Database['public']['Tables']['restaurants']['Row'];

export default function HomePage() {
  // Use TanStack Query to fetch data from the 'restaurants' table
  const {
    data: restaurants,
    isLoading,
    isError,
    error,
  } = useQuery<Restaurant[], Error>({
    queryKey: ['restaurants'], // Unique key for this query
    queryFn: async () => {
      // Fetch data from the 'restaurants' table using your Supabase client
      const { data, error } = await supabase.from('restaurants').select('*');

      if (error) {
        throw new Error(`Failed to fetch restaurants: ${error.message}`);
      }
      return data;
    },
    // Optional: Add refetchOnWindowFocus: false if you don't want it to refetch every time window is focused
    refetchOnWindowFocus: false,
  });

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <h1 className="text-3xl font-bold mb-6 text-center">
          Your Restaurants
        </h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} className="w-full">
              <CardHeader>
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4 max-w-4xl text-red-600 text-center">
        <h1 className="text-3xl font-bold mb-6">Error Loading Restaurants</h1>
        <p>There was an error connecting to Supabase or fetching data:</p>
        <p className="font-mono text-sm mt-2">{error.message}</p>
        <p className="mt-4">
          Please check your Supabase connection and API keys in `.env.local`.
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6 text-center text-gray-800">
        Your Restaurants
      </h1>

      {restaurants && restaurants.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {restaurants.map((restaurant) => (
            <Card
              key={restaurant.id}
              className="w-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg"
            >
              <CardHeader className="bg-blue-50 p-4 rounded-t-lg">
                <CardTitle className="text-xl font-semibold text-blue-800">
                  {restaurant.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 text-gray-700">
                <p className="text-sm mb-2">
                  <span className="font-medium">Address:</span>{' '}
                  {restaurant.address || 'N/A'}
                </p>
                <p className="text-sm">
                  <span className="font-medium">Phone:</span>{' '}
                  {restaurant.phone_number || 'N/A'}
                </p>
                {/* You can add more restaurant details here */}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-600 p-8 border border-gray-300 rounded-lg bg-gray-50">
          <p className="text-lg font-medium mb-2">No restaurants found.</p>
          <p className="text-sm">
            It looks like your database is empty or the connection is not
            working.
          </p>
          <p className="text-sm mt-1">
            Try running the sample data insertion script in your Supabase SQL
            Editor.
          </p>
        </div>
      )}
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useAppStore } from '@/store/appStore';
import { formatDate } from '@/lib/utils/timeCalculations';
import { RolePercentageConfig } from '@/lib/utils/tips'; // Import the type
import { format } from 'date-fns'; // Import the format function

// Define types for TipPool and Role data
type NewTipPool = Database['public']['Tables']['tip_pools']['Insert'];
type Role = Database['public']['Tables']['roles']['Row'];

export default function CreateTipPoolPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { selectedRestaurantId } = useAppStore();

  const [poolDate, setPoolDate] = useState(formatDate(new Date()));
  const [totalTips, setTotalTips] = useState<string>(''); // Keep as string for input
  const [distributionMethod, setDistributionMethod] = useState<
    'hours_worked' | 'points_system' | 'role_based_percentage'
  >('hours_worked');
  const [rolePercentages, setRolePercentages] = useState<RolePercentageConfig>(
    {}
  );
  const [totalPercentage, setTotalPercentage] = useState<number>(0);

  // Query to fetch all roles for role-based percentage distribution
  const {
    data: roles,
    isLoading: isLoadingRoles,
    isError: isErrorRoles,
    error: errorRoles,
  } = useQuery<Role[], Error>({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('role_name', { ascending: true });
      if (error) throw new Error(`Failed to fetch roles: ${error.message}`);
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // Effect to calculate total percentage for role-based distribution
  useEffect(() => {
    const sum = Object.values(rolePercentages).reduce(
      (acc, val) => acc + (Number(val) || 0),
      0
    );
    setTotalPercentage(sum);
  }, [rolePercentages]);

  // Handle change for role percentage input
  const handleRolePercentageChange = (roleId: string, value: string) => {
    const numValue = parseFloat(value);
    setRolePercentages((prev) => ({
      ...prev,
      [roleId]: isNaN(numValue) ? 0 : numValue,
    }));
  };

  // Mutation hook for inserting a new tip pool
  const createTipPoolMutation = useMutation({
    mutationFn: async (newTipPool: NewTipPool) => {
      const { data, error } = await supabase
        .from('tip_pools')
        .insert(newTipPool)
        .select()
        .single();
      if (error) {
        throw new Error(`Failed to create tip pool: ${error.message}`);
      }
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ['tipPools', selectedRestaurantId],
      });
      toast('Tip Pool Created!', {
        description: `Tip pool for ${format(new Date(data.pool_date), 'PPP')} has been scheduled.`,
      });
      router.push('/tip-pools');
    },
    onError: (error: Error) => {
      toast.error('Error creating tip pool', {
        description: error.message,
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRestaurantId) {
      toast.warning('Missing Restaurant', {
        description:
          'Please select a restaurant in the header before creating a tip pool.',
      });
      return;
    }

    const parsedTotalTips = parseFloat(totalTips);
    if (isNaN(parsedTotalTips) || parsedTotalTips <= 0) {
      toast.warning('Validation Error', {
        description: 'Total Tips must be a positive number.',
      });
      return;
    }

    if (!poolDate) {
      toast.warning('Validation Error', {
        description: 'Pool Date is required.',
      });
      return;
    }

    let config: RolePercentageConfig | null = null;
    if (distributionMethod === 'role_based_percentage') {
      if (totalPercentage !== 100) {
        toast.warning('Validation Error', {
          description: `Total percentage for role-based distribution must be 100%. Currently: ${totalPercentage}%`,
        });
        return;
      }
      // Filter out roles with 0% or invalid input before saving
      config = Object.entries(rolePercentages).reduce(
        (acc, [roleId, percentage]) => {
          if (percentage > 0) {
            acc[roleId] = percentage;
          }
          return acc;
        },
        {} as RolePercentageConfig
      );
    }

    const newTipPool: NewTipPool = {
      restaurant_id: selectedRestaurantId,
      pool_date: poolDate,
      total_tips: parsedTotalTips,
      distribution_method: distributionMethod,
      distribution_config: config, // Save the configuration
      is_distributed: false, // Always false initially
    };

    createTipPoolMutation.mutate(newTipPool);
  };

  if (isLoadingRoles) {
    return (
      <div className="container mx-auto p-4 max-w-2xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isErrorRoles) {
    toast.error('Error loading roles', {
      description: errorRoles?.message,
    });
    return (
      <div className="container mx-auto p-4 max-w-2xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Roles</h1>
        <p>Could not load roles: {errorRoles?.message}</p>
        <Button onClick={() => router.push('/tip-pools')} className="mt-4">
          Back to Tip Pools
        </Button>
      </div>
    );
  }

  if (!selectedRestaurantId) {
    return (
      <div className="container mx-auto p-4 max-w-2xl text-center">
        <Card className="shadow-lg rounded-lg p-8">
          <CardTitle className="text-2xl font-bold text-red-700 mb-4">
            No Restaurant Selected
          </CardTitle>
          <CardDescription className="text-gray-600 mb-6">
            Please select a restaurant from the dropdown in the header to create
            tip pools.
          </CardDescription>
          <Button onClick={() => router.push('/')}>Go to Dashboard</Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Create New Tip Pool
          </CardTitle>
          <CardDescription className="text-gray-600">
            Define a new tip pool for a specific date and distribution method.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="poolDate" className="text-gray-700">
                Pool Date <span className="text-red-500">*</span>
              </Label>
              <Input
                id="poolDate"
                type="date"
                value={poolDate}
                onChange={(e) => setPoolDate(e.target.value)}
                disabled={createTipPoolMutation.isPending}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
              />
            </div>

            <div>
              <Label htmlFor="totalTips" className="text-gray-700">
                Total Tips in Pool <span className="text-red-500">*</span>
              </Label>
              <Input
                id="totalTips"
                type="number"
                step="0.01"
                placeholder="e.g., 500.00"
                value={totalTips}
                onChange={(e) => setTotalTips(e.target.value)}
                disabled={createTipPoolMutation.isPending}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
              />
            </div>

            <div>
              <Label htmlFor="distributionMethod" className="text-gray-700">
                Distribution Method <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(
                  value:
                    | 'hours_worked'
                    | 'points_system'
                    | 'role_based_percentage'
                ) => setDistributionMethod(value)}
                value={distributionMethod}
                disabled={createTipPoolMutation.isPending}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select a distribution method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hours_worked">
                    Hours Worked (Equal Share)
                  </SelectItem>
                  <SelectItem value="points_system">
                    Points System (Role Weight)
                  </SelectItem>
                  <SelectItem value="role_based_percentage">
                    Role-Based Percentage
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {distributionMethod === 'role_based_percentage' && (
              <div className="space-y-4 p-4 border rounded-md bg-gray-50">
                <h3 className="text-lg font-semibold text-gray-800">
                  Role Percentages
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  Allocate a percentage of the total tip pool to each role. The
                  sum must be 100%.
                </p>
                {roles?.map((role) => (
                  <div key={role.id} className="flex items-center space-x-2">
                    <Label
                      htmlFor={`role-percentage-${role.id}`}
                      className="w-1/2"
                    >
                      {role.role_name}
                    </Label>
                    <Input
                      id={`role-percentage-${role.id}`}
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={rolePercentages[role.id] ?? ''}
                      onChange={(e) =>
                        handleRolePercentageChange(role.id, e.target.value)
                      }
                      disabled={createTipPoolMutation.isPending}
                      className="w-1/2"
                    />
                    <span className="text-gray-600">%</span>
                  </div>
                ))}
                <div className="flex justify-between items-center font-bold text-lg border-t pt-4 mt-4">
                  <span>Total Percentage:</span>
                  <span
                    className={
                      totalPercentage === 100
                        ? 'text-green-600'
                        : 'text-red-600'
                    }
                  >
                    {totalPercentage.toFixed(2)}%
                  </span>
                </div>
              </div>
            )}

            <Button
              type="submit"
              disabled={
                createTipPoolMutation.isPending ||
                !selectedRestaurantId ||
                !poolDate ||
                !totalTips ||
                (distributionMethod === 'role_based_percentage' &&
                  totalPercentage !== 100)
              }
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {createTipPoolMutation.isPending
                ? 'Creating Tip Pool...'
                : 'Create Tip Pool'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useAppStore } from '@/store/appStore';
import {
  fetchEligibleShiftsForTipPool,
  distributeTips,
  TipDistributionResult,
  RolePercentageConfig,
  ShiftWithDetails,
} from '@/lib/utils/tips';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { CheckCircle2, XCircle, Loader2 } from 'lucide-react';
import { Label } from '@/components/ui/label';

// Define types for TipPool, TipDistribution, and Role data
type TipPool = Database['public']['Tables']['tip_pools']['Row'];
type TipDistribution = Database['public']['Tables']['tip_distributions']['Row'];
type NewTipDistribution =
  Database['public']['Tables']['tip_distributions']['Insert'];
type Role = Database['public']['Tables']['roles']['Row']; // Added Role type

export default function TipPoolDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const tipPoolId = params.id as string;
  const queryClient = useQueryClient();
  const { selectedRestaurantId } = useAppStore();

  const [distributionResults, setDistributionResults] = useState<
    TipDistributionResult[] | null
  >(null);

  // 1. Query to fetch the specific tip pool details
  const {
    data: tipPool,
    isLoading: isLoadingTipPool,
    isError: isErrorTipPool,
    error: errorTipPool,
  } = useQuery<TipPool, Error>({
    queryKey: ['tipPool', tipPoolId],
    queryFn: async () => {
      if (!tipPoolId) throw new Error('Tip Pool ID is missing.');
      const { data, error } = await supabase
        .from('tip_pools')
        .select('*')
        .eq('id', tipPoolId)
        .single();
      if (error) throw new Error(`Failed to fetch tip pool: ${error.message}`);
      return data;
    },
    enabled: !!tipPoolId,
    refetchOnWindowFocus: false,
  });

  // 2. Query to fetch existing tip distributions for this pool
  const {
    data: existingDistributions,
    isLoading: isLoadingExistingDistributions,
    isError: isErrorExistingDistributions,
    error: errorExistingDistributions,
  } = useQuery<TipDistribution[], Error>({
    queryKey: ['tipDistributions', tipPoolId],
    queryFn: async () => {
      if (!tipPoolId) return [];
      const { data, error } = await supabase
        .from('tip_distributions')
        .select(
          `
          *,
          employees (
            first_name,
            last_name
          ),
          tip_pools (
            distribution_method,
            distribution_config
          )
        `
        )
        .eq('tip_pool_id', tipPoolId)
        .order('distributed_at', { ascending: true }); // Order by distribution time

      if (error)
        throw new Error(
          `Failed to fetch existing distributions: ${error.message}`
        );

      // Map existing distributions to TipDistributionResult format for consistent display
      return data.map((d) => ({
        employee_id: d.employee_id,
        employee_name: `${d.employees?.first_name} ${d.employees?.last_name || ''}`,
        role_name: 'N/A', // Role name is not directly stored in tip_distributions, would need another join or derived
        hours_worked: 0, // Not stored in tip_distributions, would need to re-calculate or store
        amount_distributed: d.amount_distributed,
      }));
    },
    enabled: !!tipPoolId && tipPool?.is_distributed === true, // Only fetch if pool is distributed
    refetchOnWindowFocus: false,
  });

  // 3. Query to fetch all roles (needed for displaying role names in distribution config)
  const {
    data: roles,
    isLoading: isLoadingRoles,
    isError: isErrorRoles,
    error: errorRoles,
  } = useQuery<Role[], Error>({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('role_name', { ascending: true });
      if (error) throw new Error(`Failed to fetch roles: ${error.message}`);
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // Set distribution results from existing data if pool is already distributed
  useEffect(() => {
    if (tipPool?.is_distributed && existingDistributions) {
      setDistributionResults(existingDistributions);
    }
  }, [tipPool, existingDistributions]);

  // Mutation hook for performing tip distribution
  const distributeTipsMutation = useMutation({
    mutationFn: async ({
      poolId,
      restaurantId,
      poolDate,
      totalTips,
      distributionMethod,
      distributionConfig,
    }: {
      poolId: string;
      restaurantId: string;
      poolDate: string;
      totalTips: number;
      distributionMethod:
        | 'hours_worked'
        | 'points_system'
        | 'role_based_percentage';
      distributionConfig: RolePercentageConfig | null;
    }) => {
      console.log('DistributeTipsMutation: Starting mutationFn');
      console.log('DistributeTipsMutation: Inputs:', {
        poolId,
        restaurantId,
        poolDate,
        totalTips,
        distributionMethod,
        distributionConfig,
      });

      // Fetch eligible shifts
      const eligibleShifts: ShiftWithDetails[] =
        await fetchEligibleShiftsForTipPool(restaurantId, poolDate);
      console.log(
        'DistributeTipsMutation: Fetched eligible shifts:',
        eligibleShifts
      );

      // Perform the distribution calculation
      const calculatedDistributions: TipDistributionResult[] = distributeTips(
        totalTips,
        eligibleShifts,
        distributionMethod,
        distributionConfig || undefined // Pass undefined if null
      );
      console.log(
        'DistributeTipsMutation: Calculated distributions:',
        calculatedDistributions
      );

      if (calculatedDistributions.length === 0) {
        throw new Error('No eligible employees or hours for distribution.');
      }

      // Prepare data for insertion into tip_distributions table
      const newDistributions: NewTipDistribution[] =
        calculatedDistributions.map((result) => ({
          tip_pool_id: poolId,
          employee_id: result.employee_id,
          amount_distributed: result.amount_distributed,
        }));
      console.log(
        'DistributeTipsMutation: Prepared new distributions for insert:',
        newDistributions
      );

      // Insert distributions in a transaction-like manner
      const { error: insertError } = await supabase
        .from('tip_distributions')
        .insert(newDistributions);

      if (insertError) {
        console.error(
          'DistributeTipsMutation: Error inserting distributions:',
          insertError
        );
        throw new Error(
          `Failed to save tip distributions: ${insertError.message}`
        );
      }
      console.log(
        'DistributeTipsMutation: Distributions inserted successfully.'
      );

      // Update the tip_pool status to distributed
      const { error: updatePoolError } = await supabase
        .from('tip_pools')
        .update({ is_distributed: true })
        .eq('id', poolId);

      if (updatePoolError) {
        console.error(
          'DistributeTipsMutation: Failed to update tip pool status to distributed:',
          updatePoolError.message
        );
        // If updating pool status fails, consider rolling back distributions (complex, but ideal)
        // For now, just log the error and proceed, but acknowledge potential inconsistency
      }
      console.log(
        'DistributeTipsMutation: Tip pool status updated to distributed.'
      );

      return calculatedDistributions; // Return the calculated results for display
    },
    onSuccess: (results) => {
      console.log('DistributeTipsMutation: onSuccess - Results:', results);
      setDistributionResults(results); // Display the results
      queryClient.invalidateQueries({ queryKey: ['tipPool', tipPoolId] }); // Refetch tip pool to update is_distributed status
      queryClient.invalidateQueries({
        queryKey: ['tipDistributions', tipPoolId],
      }); // Refetch existing distributions
      toast('Tips Distributed!', {
        description: `Successfully distributed tips for ${format(new Date(tipPool?.pool_date || ''), 'PPP')}.`,
      });
    },
    onError: (error: Error) => {
      console.error('DistributeTipsMutation: onError - Error:', error);
      toast.error('Error distributing tips', {
        description: error.message,
      });
    },
  });

  const handleDistributeTips = () => {
    console.log('handleDistributeTips: Button clicked.');
    if (!tipPool || !selectedRestaurantId) {
      console.warn(
        'handleDistributeTips: Missing tipPool or selectedRestaurantId.'
      );
      toast.error('Missing Data', {
        description:
          'Cannot distribute tips: Tip pool or restaurant not selected.',
      });
      return;
    }

    if (tipPool.is_distributed) {
      console.warn('handleDistributeTips: Tip pool already distributed.');
      toast.warning('Already Distributed', {
        description: 'This tip pool has already been distributed.',
      });
      return;
    }

    // Ensure distribution_config is parsed correctly if it's a JSON string
    let config: RolePercentageConfig | null = null;
    if (
      tipPool.distribution_method === 'role_based_percentage' &&
      tipPool.distribution_config
    ) {
      try {
        // Supabase returns JSONB as a plain object, no need to JSON.parse
        config = tipPool.distribution_config as RolePercentageConfig;
        console.log(
          'handleDistributeTips: Parsed distribution_config:',
          config
        );
      } catch (e) {
        console.error(
          'handleDistributeTips: Error parsing distribution_config:',
          e
        );
        toast.error('Configuration Error', {
          description: 'Invalid distribution configuration for this tip pool.',
        });
        return;
      }
    }

    console.log(
      'handleDistributeTips: Calling distributeTipsMutation.mutate with:',
      {
        poolId: tipPool.id,
        restaurantId: selectedRestaurantId,
        poolDate: tipPool.pool_date,
        totalTips: tipPool.total_tips,
        distributionMethod: tipPool.distribution_method as
          | 'hours_worked'
          | 'points_system'
          | 'role_based_percentage',
        distributionConfig: config,
      }
    );
    distributeTipsMutation.mutate({
      poolId: tipPool.id,
      restaurantId: selectedRestaurantId,
      poolDate: tipPool.pool_date,
      totalTips: tipPool.total_tips,
      distributionMethod: tipPool.distribution_method as
        | 'hours_worked'
        | 'points_system'
        | 'role_based_percentage',
      distributionConfig: config,
    });
  };

  if (isLoadingTipPool || isLoadingExistingDistributions || isLoadingRoles) {
    // Added isLoadingRoles
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isErrorTipPool || isErrorExistingDistributions || isErrorRoles) {
    // Added isErrorRoles
    const errorMessage =
      errorTipPool?.message ||
      errorExistingDistributions?.message ||
      errorRoles?.message ||
      'Unknown error';
    toast.error('Error loading tip pool details', {
      description: errorMessage,
    });
    return (
      <div className="container mx-auto p-4 max-w-4xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Tip Pool</h1>
        <p>Could not load tip pool details: {errorMessage}</p>
        <Button onClick={() => router.push('/tip-pools')} className="mt-4">
          Back to Tip Pools
        </Button>
      </div>
    );
  }

  if (!tipPool) {
    return (
      <div className="container mx-auto p-4 max-w-4xl text-center">
        <h1 className="text-2xl font-bold mb-4">Tip Pool Not Found</h1>
        <p>The tip pool you are trying to view does not exist.</p>
        <Button onClick={() => router.push('/tip-pools')} className="mt-4">
          Back to Tip Pools
        </Button>
      </div>
    );
  }

  const displayMethod = tipPool.distribution_method
    ? tipPool.distribution_method.replace(/_/g, ' ')
    : 'N/A';

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Tip Pool Details for {format(new Date(tipPool.pool_date), 'PPP')}
          </CardTitle>
          <CardDescription className="text-gray-600">
            Overview of the tip pool and its distribution status.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="font-semibold text-gray-700">Total Tips:</Label>
              <p className="text-lg text-gray-900">
                ${tipPool.total_tips.toFixed(2)}
              </p>
            </div>
            <div>
              <Label className="font-semibold text-gray-700">
                Distribution Method:
              </Label>
              <p className="text-lg text-gray-900">{displayMethod}</p>
            </div>
          </div>

          {tipPool.distribution_method === 'role_based_percentage' &&
            tipPool.distribution_config && (
              <div className="space-y-2 p-4 border rounded-md bg-gray-50">
                <h4 className="text-md font-semibold text-gray-800">
                  Distribution Configuration (Role Percentages):
                </h4>
                {/* Ensure roles are loaded before mapping */}
                {roles &&
                  Object.entries(
                    tipPool.distribution_config as RolePercentageConfig
                  ).map(([roleId, percentage]) => {
                    const role = roles.find((r) => r.id === roleId); // Find role from fetched roles
                    return (
                      <p key={roleId} className="text-sm text-gray-700">
                        <span className="font-medium">
                          {role?.role_name || 'Unknown Role'}:
                        </span>{' '}
                        {percentage}%
                      </p>
                    );
                  })}
                {!roles && (
                  <p className="text-sm text-gray-500">
                    Loading role configuration...
                  </p>
                )}
              </div>
            )}

          <div className="flex items-center text-lg">
            <Label className="font-semibold text-gray-700 mr-2">
              Distributed Status:
            </Label>
            {tipPool.is_distributed ? (
              <span className="inline-flex items-center text-green-600">
                <CheckCircle2 className="h-5 w-5 mr-1" /> Distributed
              </span>
            ) : (
              <span className="inline-flex items-center text-red-600">
                <XCircle className="h-5 w-5 mr-1" /> Not Distributed
              </span>
            )}
          </div>

          {!tipPool.is_distributed && (
            <Button
              onClick={handleDistributeTips}
              disabled={distributeTipsMutation.isPending}
              className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {distributeTipsMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Distributing
                  Tips...
                </>
              ) : (
                'Distribute Tips Now'
              )}
            </Button>
          )}

          {distributionResults && distributionResults.length > 0 && (
            <div className="mt-8">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                Distribution Results
              </h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Hours Worked</TableHead>
                    {tipPool.distribution_method === 'points_system' && (
                      <TableHead>Weighted Hours</TableHead>
                    )}
                    <TableHead className="text-right">
                      Amount Received
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {distributionResults.map((result) => (
                    <TableRow key={result.employee_id}>
                      <TableCell className="font-medium">
                        {result.employee_name}
                      </TableCell>
                      <TableCell>{result.role_name}</TableCell>
                      <TableCell>{result.hours_worked.toFixed(2)}</TableCell>
                      {tipPool.distribution_method === 'points_system' && (
                        <TableCell>
                          {result.weighted_hours?.toFixed(2) || 'N/A'}
                        </TableCell>
                      )}
                      <TableCell className="text-right">
                        ${result.amount_distributed.toFixed(2)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

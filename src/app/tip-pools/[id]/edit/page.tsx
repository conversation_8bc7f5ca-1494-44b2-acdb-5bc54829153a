'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils'; // Utility for conditional class names

// Define types for TipPool and Role data
type TipPool = Database['public']['Tables']['tip_pools']['Row'];
type UpdateTipPool = Database['public']['Tables']['tip_pools']['Update'];
type Role = Database['public']['Tables']['roles']['Row'];
type RolePercentageConfig = { [roleId: string]: number };

export default function EditTipPoolPage() {
  const router = useRouter();
  const params = useParams();
  const tipPoolId = params.id as string;
  const queryClient = useQueryClient();

  const [poolDate, setPoolDate] = useState<Date | undefined>(undefined);
  const [totalTips, setTotalTips] = useState<string>('');
  const [distributionMethod, setDistributionMethod] = useState<
    'hours_worked' | 'points_system' | 'role_based_percentage' | ''
  >('');
  const [isDistributed, setIsDistributed] = useState(false);
  const [rolePercentages, setRolePercentages] = useState<RolePercentageConfig>(
    {}
  );
  const [totalPercentage, setTotalPercentage] = useState<number>(0); // New state for total percentage

  // 1. Query to fetch the existing tip pool data
  const {
    data: tipPool,
    isLoading: isLoadingTipPool,
    isError: isErrorTipPool,
    error: errorTipPool,
  } = useQuery<TipPool, Error>({
    queryKey: ['tipPool', tipPoolId],
    queryFn: async () => {
      if (!tipPoolId) throw new Error('Tip Pool ID is missing.');
      const { data, error } = await supabase
        .from('tip_pools')
        .select('*')
        .eq('id', tipPoolId)
        .single();

      if (error) {
        throw new Error(`Failed to fetch tip pool: ${error.message}`);
      }
      return data;
    },
    enabled: !!tipPoolId,
    refetchOnWindowFocus: false,
  });

  // 2. Query to fetch all roles (needed for role-based percentage distribution)
  const {
    data: roles,
    isLoading: isLoadingRoles,
    isError: isErrorRoles,
    error: errorRoles,
  } = useQuery<Role[], Error>({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('role_name', { ascending: true });
      if (error) throw new Error(`Failed to fetch roles: ${error.message}`);
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // Populate form fields when tip pool data is loaded
  useEffect(() => {
    if (tipPool) {
      setPoolDate(new Date(tipPool.pool_date));
      setTotalTips(tipPool.total_tips.toString());
      setDistributionMethod(
        tipPool.distribution_method as typeof distributionMethod
      );
      setIsDistributed(tipPool.is_distributed);

      // If role_based_percentage, parse and set rolePercentages
      if (
        tipPool.distribution_method === 'role_based_percentage' &&
        tipPool.distribution_config
      ) {
        setRolePercentages(tipPool.distribution_config as RolePercentageConfig);
      } else {
        setRolePercentages({}); // Clear if not role-based
      }
    }
  }, [tipPool]);

  // Effect to calculate total percentage for role-based distribution
  useEffect(() => {
    const sum = Object.values(rolePercentages).reduce(
      (acc, val) => acc + (Number(val) || 0),
      0
    );
    setTotalPercentage(sum);
  }, [rolePercentages]);

  // Handle change for role percentage inputs
  const handleRolePercentageChange = (roleId: string, value: string) => {
    const percentage = parseFloat(value);
    setRolePercentages((prev) => ({
      ...prev,
      [roleId]: isNaN(percentage) ? 0 : percentage, // Store 0 if not a valid number
    }));
  };

  // 3. Mutation to update the tip pool data
  const updateTipPoolMutation = useMutation({
    mutationFn: async (updatedFields: UpdateTipPool) => {
      if (!tipPoolId) throw new Error('Tip Pool ID is missing for update.');

      // Ensure distribution_config is stringified if it's an object
      const payload = { ...updatedFields };
      if (
        payload.distribution_config &&
        typeof payload.distribution_config === 'object'
      ) {
        payload.distribution_config = JSON.stringify(
          payload.distribution_config
        );
      } else if (payload.distribution_method !== 'role_based_percentage') {
        // If method changes and it's not role_based, ensure config is null
        payload.distribution_config = null;
      }

      const { data, error } = await supabase
        .from('tip_pools')
        .update(payload)
        .eq('id', tipPoolId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update tip pool: ${error.message}`);
      }
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['tipPool', tipPoolId] });
      queryClient.invalidateQueries({ queryKey: ['tipPools'] });
      toast('Tip Pool Updated!', {
        description: `Tip pool for ${format(new Date(data.pool_date), 'PPP')} has been successfully updated.`,
      });
      router.push('/tip-pools'); // Redirect back to the tip pools list
    },
    onError: (error: Error) => {
      toast.error('Error updating tip pool', {
        description: error.message,
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!poolDate || !totalTips || !distributionMethod) {
      toast.warning('Validation Error', {
        description: 'All fields are required.',
      });
      return;
    }

    const parsedTotalTips = parseFloat(totalTips);
    if (isNaN(parsedTotalTips) || parsedTotalTips <= 0) {
      toast.warning('Validation Error', {
        description: 'Total Tips must be a positive number.',
      });
      return;
    }

    let configToSave: RolePercentageConfig | null = null;
    if (distributionMethod === 'role_based_percentage') {
      if (totalPercentage !== 100) {
        toast.warning('Validation Error', {
          description: `Total role percentages must add up to 100%. Current total: ${totalPercentage}%`,
        });
        return;
      }
      configToSave = rolePercentages;
    }

    const updatedFields: UpdateTipPool = {
      pool_date: format(poolDate, 'yyyy-MM-dd'),
      total_tips: parsedTotalTips,
      distribution_method: distributionMethod,
      distribution_config: configToSave, // Will be stringified in mutationFn
    };

    updateTipPoolMutation.mutate(updatedFields);
  };

  // Determine if the save button should be disabled
  const isSaveDisabled =
    updateTipPoolMutation.isPending ||
    isDistributed ||
    (distributionMethod === 'role_based_percentage' && totalPercentage !== 100);

  if (isLoadingTipPool || isLoadingRoles) {
    return (
      <div className="container mx-auto p-4 max-w-2xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isErrorTipPool || isErrorRoles) {
    const errorMessage =
      errorTipPool?.message || errorRoles?.message || 'Unknown error';
    toast.error('Error loading data', {
      description: errorMessage,
    });
    return (
      <div className="container mx-auto p-4 max-w-2xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Tip Pool Data</h1>
        <p>Could not load tip pool details or roles: {errorMessage}</p>
        <Button onClick={() => router.push('/tip-pools')} className="mt-4">
          Back to Tip Pools
        </Button>
      </div>
    );
  }

  if (!tipPool) {
    return (
      <div className="container mx-auto p-4 max-w-2xl text-center">
        <h1 className="text-2xl font-bold mb-4">Tip Pool Not Found</h1>
        <p>The tip pool you are trying to edit does not exist.</p>
        <Button onClick={() => router.push('/tip-pools')} className="mt-4">
          Back to Tip Pools
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Edit Tip Pool
          </CardTitle>
          <CardDescription className="text-gray-600">
            Update the details for the tip pool on{' '}
            {format(new Date(tipPool.pool_date), 'PPP')}.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="poolDate" className="text-gray-700">
                Pool Date <span className="text-red-500">*</span>
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={'outline'}
                    className={cn(
                      'w-full justify-start text-left font-normal mt-1',
                      !poolDate && 'text-muted-foreground'
                    )}
                    disabled={updateTipPoolMutation.isPending || isDistributed} // Disable if distributed
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {poolDate ? (
                      format(poolDate, 'PPP')
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={poolDate}
                    onSelect={setPoolDate}
                    initialFocus
                    disabled={isDistributed} // Disable if distributed
                  />
                </PopoverContent>
              </Popover>
              {isDistributed && (
                <p className="text-sm text-red-500 mt-1">
                  Cannot change date for distributed tip pool.
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="totalTips" className="text-gray-700">
                Total Tips <span className="text-red-500">*</span>
              </Label>
              <Input
                id="totalTips"
                type="number"
                step="0.01"
                placeholder="e.g., 500.00"
                value={totalTips}
                onChange={(e) => setTotalTips(e.target.value)}
                disabled={updateTipPoolMutation.isPending || isDistributed} // Disable if distributed
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
              />
              {isDistributed && (
                <p className="text-sm text-red-500 mt-1">
                  Cannot change total tips for distributed tip pool.
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="distributionMethod" className="text-gray-700">
                Distribution Method <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value: typeof distributionMethod) => {
                  setDistributionMethod(value);
                  // Clear role percentages if method changes from role-based
                  if (value !== 'role_based_percentage') {
                    setRolePercentages({});
                  }
                }}
                value={distributionMethod}
                disabled={updateTipPoolMutation.isPending || isDistributed} // Disable if distributed
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select a distribution method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hours_worked">Hours Worked</SelectItem>
                  <SelectItem value="points_system">Points System</SelectItem>
                  <SelectItem value="role_based_percentage">
                    Role-Based Percentage
                  </SelectItem>
                </SelectContent>
              </Select>
              {isDistributed && (
                <p className="text-sm text-red-500 mt-1">
                  Cannot change distribution method for distributed tip pool.
                </p>
              )}
            </div>

            {distributionMethod === 'role_based_percentage' && (
              <div className="space-y-4 p-4 border rounded-md bg-gray-50">
                <h3 className="text-lg font-semibold text-gray-800">
                  Role Percentages
                </h3>
                <p className="text-sm text-gray-600">
                  Assign a percentage of the total tips to each role. The sum
                  must equal 100%.
                </p>
                {roles?.map((role) => (
                  <div key={role.id} className="flex items-center space-x-2">
                    <Label
                      htmlFor={`role-${role.id}`}
                      className="w-1/2 text-gray-700"
                    >
                      {role.role_name}:
                    </Label>
                    <Input
                      id={`role-${role.id}`}
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={
                        rolePercentages[role.id] !== undefined
                          ? rolePercentages[role.id].toString()
                          : ''
                      }
                      onChange={(e) =>
                        handleRolePercentageChange(role.id, e.target.value)
                      }
                      disabled={
                        updateTipPoolMutation.isPending || isDistributed
                      }
                      className="w-1/2 rounded-md border-gray-300 shadow-sm"
                    />
                    <span className="text-gray-600">%</span>
                  </div>
                ))}
                <div className="text-right font-bold text-lg border-t pt-4 mt-4">
                  <span>Total:</span>
                  <span
                    className={
                      totalPercentage === 100
                        ? 'text-green-600'
                        : 'text-red-600'
                    }
                  >
                    {totalPercentage.toFixed(2)}%
                  </span>
                </div>
                {totalPercentage !== 100 && (
                  <p className="text-sm text-red-500 mt-2 text-center">
                    Total percentage must be 100% to save changes.
                  </p>
                )}
              </div>
            )}

            <Button
              type="submit"
              disabled={isSaveDisabled} // Use the new computed disabled state
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {updateTipPoolMutation.isPending
                ? 'Saving Changes...'
                : 'Save Changes'}
            </Button>
            {isDistributed && (
              <p className="text-sm text-red-500 mt-2 text-center">
                This tip pool has already been distributed and cannot be edited.
              </p>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

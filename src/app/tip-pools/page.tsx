// src/app/tip-pools/page.tsx
'use client';

import { useRouter } from 'next/navigation'; // Removed useState, useEffect
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, CheckCircle2, XCircle } from 'lucide-react';
import { useAppStore } from '@/store/appStore';
import { format } from 'date-fns';

// Define types for TipPool data
type TipPool = Database['public']['Tables']['tip_pools']['Row'];

export default function TipPoolsListPage() {
  const queryClient = useQueryClient();
  const { selectedRestaurantId } = useAppStore();

  // Call useRouter unconditionally at the top level
  const router = useRouter();

  // Query to fetch tip pools for the selected restaurant
  const {
    data: tipPools,
    isLoading,
    isError,
    error,
  } = useQuery<TipPool[], Error>({
    queryKey: ['tipPools', selectedRestaurantId],
    queryFn: async () => {
      if (!selectedRestaurantId) return [];
      const { data, error } = await supabase
        .from('tip_pools')
        .select('*')
        .eq('restaurant_id', selectedRestaurantId)
        .order('pool_date', { ascending: false }); // Order by most recent first

      if (error) {
        throw new Error(`Failed to fetch tip pools: ${error.message}`);
      }
      return data;
    },
    enabled: !!selectedRestaurantId,
    refetchOnWindowFocus: false,
  });

  // Mutation hook for deleting a tip pool
  const deleteTipPoolMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase.from('tip_pools').delete().eq('id', id);
      if (error) {
        throw new Error(`Failed to delete tip pool: ${error.message}`);
      }
      return id;
    },
    onSuccess: (deletedId) => {
      queryClient.invalidateQueries({
        queryKey: ['tipPools', selectedRestaurantId],
      });
      toast('Tip Pool Deleted!', {
        description: `Tip pool with ID ${deletedId.substring(0, 8)}... has been removed.`,
      });
    },
    onError: (error: Error) => {
      toast.error('Error deleting tip pool', {
        description: error.message,
      });
    },
  });

  const handleDelete = (id: string) => {
    deleteTipPoolMutation.mutate(id);
  };

  // No need for isMounted check here, as useRouter is now unconditional
  // and the component will only render on the client after hydration.

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 max-w-6xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Tip Pools</h1>
          <Skeleton className="h-10 w-40 rounded-md" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} className="w-full">
              <CardHeader>
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    toast.error('Error loading tip pools', {
      description: error.message,
    });
    return (
      <div className="container mx-auto p-4 max-w-6xl text-red-600 text-center">
        <h1 className="text-3xl font-bold mb-6">Error Loading Tip Pools</h1>
        <p>There was an error connecting to Supabase or fetching data:</p>
        <p className="font-mono text-sm mt-2">{error.message}</p>
        <p className="mt-4">
          Please ensure your database is running and accessible.
        </p>
      </div>
    );
  }

  if (!selectedRestaurantId) {
    return (
      <div className="container mx-auto p-4 max-w-6xl text-center">
        <Card className="shadow-lg rounded-lg p-8">
          <CardTitle className="text-2xl font-bold text-red-700 mb-4">
            No Restaurant Selected
          </CardTitle>
          <CardDescription className="text-gray-600 mb-6">
            Please select a restaurant from the dropdown in the header to manage
            tip pools.
          </CardDescription>
          <Button onClick={() => router.push('/')}>Go to Dashboard</Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Tip Pools</h1>
        <Link href="/tip-pools/create" passHref>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-md">
            Create New Tip Pool
          </Button>
        </Link>
      </div>

      {tipPools && tipPools.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tipPools.map((pool) => (
            <Card
              key={pool.id}
              className="w-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg"
            >
              <CardHeader className="bg-blue-50 p-4 rounded-t-lg flex flex-row items-center justify-between">
                <CardTitle className="text-xl font-semibold text-blue-800">
                  Tip Pool - {format(new Date(pool.pool_date), 'PPP')}
                </CardTitle>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link href={`/tip-pools/${pool.id}/details`}>
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    {/* Only allow editing if not yet distributed */}
                    {!pool.is_distributed && (
                      <DropdownMenuItem asChild>
                        <Link href={`/tip-pools/${pool.id}/edit`}>Edit</Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <DropdownMenuItem
                          onSelect={(e) => e.preventDefault()}
                          className="text-red-600 focus:text-red-600"
                        >
                          Delete
                        </DropdownMenuItem>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Are you absolutely sure?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. This will permanently
                            delete the tip pool and all associated distribution
                            records.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(pool.id)}
                            className="bg-red-600 hover:bg-red-700 text-white"
                            disabled={deleteTipPoolMutation.isPending}
                          >
                            {deleteTipPoolMutation.isPending
                              ? 'Deleting...'
                              : 'Continue'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent className="p-4 text-gray-700">
                <p className="text-sm mb-2">
                  <span className="font-medium">Total Tips:</span> $
                  {pool.total_tips.toFixed(2)}
                </p>
                <p className="text-sm mb-2">
                  <span className="font-medium">Method:</span>{' '}
                  {pool.distribution_method
                    ? pool.distribution_method.replace(/_/g, ' ')
                    : 'N/A'}
                </p>
                <div className="flex items-center text-sm mt-2">
                  <span className="font-medium mr-2">Distributed:</span>
                  {pool.is_distributed ? (
                    <span className="inline-flex items-center text-green-600">
                      <CheckCircle2 className="h-4 w-4 mr-1" /> Yes
                    </span>
                  ) : (
                    <span className="inline-flex items-center text-red-600">
                      <XCircle className="h-4 w-4 mr-1" /> No
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-600 p-8 border border-gray-300 rounded-lg bg-gray-50">
          <p className="text-lg font-medium mb-2">No tip pools found.</p>
          <p className="text-sm">
            Start by creating your first tip pool using the button above!
          </p>
        </div>
      )}
    </div>
  );
}

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';

// Define types for Employee, Restaurant, and Role data
type NewEmployee = Database['public']['Tables']['employees']['Insert'];
type Restaurant = Database['public']['Tables']['restaurants']['Row'];
type Role = Database['public']['Tables']['roles']['Row']; // Added Role type

export default function CreateEmployeePage() {
  const router = useRouter();
  const queryClient = useQueryClient();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [position, setPosition] = useState(''); // This will be the employee's general position (now from roles dropdown)
  const [restaurantId, setRestaurantId] = useState<string | null>(null);
  const [isActive, setIsActive] = useState(true);

  // Query to fetch available restaurants for the dropdown
  const {
    data: restaurants,
    isLoading: isLoadingRestaurants,
    isError: isErrorRestaurants,
    error: errorRestaurants,
  } = useQuery<Restaurant[], Error>({
    queryKey: ['restaurants'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .order('name', { ascending: true });
      if (error) {
        throw new Error(`Failed to fetch restaurants: ${error.message}`);
      }
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // New Query to fetch available roles for the dropdown
  const {
    data: roles,
    isLoading: isLoadingRoles,
    isError: isErrorRoles,
    error: errorRoles,
  } = useQuery<Role[], Error>({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('role_name', { ascending: true });
      if (error) {
        throw new Error(`Failed to fetch roles: ${error.message}`);
      }
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // Mutation hook for inserting a new employee
  const createEmployeeMutation = useMutation({
    mutationFn: async (newEmployee: NewEmployee) => {
      const { data, error } = await supabase
        .from('employees')
        .insert(newEmployee)
        .select()
        .single();
      if (error) {
        throw new Error(`Failed to create employee: ${error.message}`);
      }
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['employees'] }); // Invalidate employee list
      toast('Employee Created!', {
        description: `${data.first_name} ${data.last_name} has been successfully added.`,
      });
      router.push('/employees'); // Redirect to the employees list page
    },
    onError: (error: Error) => {
      toast.error('Error creating employee', {
        description: error.message,
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!firstName || !lastName || !restaurantId || !position) {
      toast.warning('Validation Error', {
        description:
          'First Name, Last Name, Position, and Restaurant are required.',
      });
      return;
    }

    const newEmployee: NewEmployee = {
      first_name: firstName,
      last_name: lastName,
      position: position, // Using the selected position directly from roles dropdown
      restaurant_id: restaurantId,
      is_active: isActive,
    };

    createEmployeeMutation.mutate(newEmployee);
  };

  if (isLoadingRestaurants || isLoadingRoles) {
    // Combined loading state
    return (
      <div className="container mx-auto p-4 max-w-2xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isErrorRestaurants || isErrorRoles) {
    // Combined error state
    const errorMessage =
      errorRestaurants?.message || errorRoles?.message || 'Unknown error';
    toast.error('Error loading data for selection', {
      description: errorMessage,
    });
    return (
      <div className="container mx-auto p-4 max-w-2xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Data</h1>
        <p>
          Could not load necessary lists (restaurants or roles): {errorMessage}
        </p>
        <Button onClick={() => router.push('/employees')} className="mt-4">
          Back to Employees
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Add New Employee
          </CardTitle>
          <CardDescription className="text-gray-600">
            Enter the details for the new employee.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName" className="text-gray-700">
                  First Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="firstName"
                  type="text"
                  placeholder="e.g., Jane"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  disabled={createEmployeeMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
              <div>
                <Label htmlFor="lastName" className="text-gray-700">
                  Last Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="lastName"
                  type="text"
                  placeholder="e.g., Doe"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  disabled={createEmployeeMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="restaurant" className="text-gray-700">
                Restaurant <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => setRestaurantId(value)}
                value={restaurantId || ''}
                disabled={
                  createEmployeeMutation.isPending || isLoadingRestaurants
                }
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select a restaurant" />
                </SelectTrigger>
                <SelectContent>
                  {restaurants?.map((r) => (
                    <SelectItem key={r.id} value={r.id}>
                      {r.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="position" className="text-gray-700">
                Position (Default Role) <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => setPosition(value)}
                value={position || ''}
                disabled={createEmployeeMutation.isPending || isLoadingRoles}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select a position/role" />
                </SelectTrigger>
                <SelectContent>
                  {roles?.map((r) => (
                    <SelectItem key={r.id} value={r.role_name}>
                      {r.role_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked) => setIsActive(!!checked)}
                disabled={createEmployeeMutation.isPending}
              />
              <Label htmlFor="isActive" className="text-gray-700">
                Is Active?
              </Label>
            </div>

            <Button
              type="submit"
              disabled={
                createEmployeeMutation.isPending ||
                !restaurantId ||
                !firstName ||
                !lastName ||
                !position
              }
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {createEmployeeMutation.isPending
                ? 'Adding Employee...'
                : 'Add Employee'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

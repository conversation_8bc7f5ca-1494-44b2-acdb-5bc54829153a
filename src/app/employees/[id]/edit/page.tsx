'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';

// Define types for Employee, Restaurant, and Role data
type Employee = Database['public']['Tables']['employees']['Row'];
type UpdateEmployee = Database['public']['Tables']['employees']['Update'];
type Restaurant = Database['public']['Tables']['restaurants']['Row'];
type Role = Database['public']['Tables']['roles']['Row'];

export default function EditEmployeePage() {
  const router = useRouter();
  const params = useParams();
  const employeeId = params.id as string;
  const queryClient = useQueryClient();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [position, setPosition] = useState('');
  const [restaurantId, setRestaurantId] = useState<string | null>(null);
  const [isActive, setIsActive] = useState<boolean>(true);

  // 1. Query to fetch the existing employee data
  const {
    data: employee,
    isLoading: isLoadingEmployee,
    isError: isErrorEmployee,
    error: errorEmployee,
  } = useQuery<Employee, Error>({
    queryKey: ['employee', employeeId],
    queryFn: async () => {
      if (!employeeId) throw new Error('Employee ID is missing.');
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .eq('id', employeeId)
        .single();

      if (error) {
        throw new Error(`Failed to fetch employee: ${error.message}`);
      }
      return data;
    },
    enabled: !!employeeId, // Only run this query if employeeId is available
    refetchOnWindowFocus: false,
  });

  // 2. Query to fetch available restaurants for the dropdown
  const {
    data: restaurants,
    isLoading: isLoadingRestaurants,
    isError: isErrorRestaurants,
    error: errorRestaurants,
  } = useQuery<Restaurant[], Error>({
    queryKey: ['restaurants'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .order('name', { ascending: true });
      if (error) {
        throw new Error(`Failed to fetch restaurants: ${error.message}`);
      }
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // 3. Query to fetch available roles for the dropdown
  const {
    data: roles,
    isLoading: isLoadingRoles,
    isError: isErrorRoles,
    error: errorRoles,
  } = useQuery<Role[], Error>({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('role_name', { ascending: true });
      if (error) {
        throw new Error(`Failed to fetch roles: ${error.message}`);
      }
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // Populate form fields when employee data is loaded
  useEffect(() => {
    if (employee) {
      setFirstName(employee.first_name);
      setLastName(employee.last_name);
      setPosition(employee.position || '');
      setRestaurantId(employee.restaurant_id);
      setIsActive(employee?.is_active ?? true);
    }
  }, [employee]);

  // 4. Mutation to update the employee data
  const updateEmployeeMutation = useMutation({
    mutationFn: async (updatedFields: UpdateEmployee) => {
      if (!employeeId) throw new Error('Employee ID is missing for update.');
      const { data, error } = await supabase
        .from('employees')
        .update(updatedFields)
        .eq('id', employeeId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update employee: ${error.message}`);
      }
      return data;
    },
    onSuccess: (data) => {
      // Invalidate both the specific employee query and the list query
      queryClient.invalidateQueries({ queryKey: ['employee', employeeId] });
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast('Employee Updated!', {
        description: `${data.first_name} ${data.last_name} has been successfully updated.`,
      });
      router.push('/employees'); // Redirect back to the employees list
    },
    onError: (error: Error) => {
      toast.error('Error updating employee', {
        description: error.message,
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!firstName || !lastName || !restaurantId || !position) {
      toast.warning('Validation Error', {
        description:
          'First Name, Last Name, Position, and Restaurant are required.',
      });
      return;
    }

    const updatedFields: UpdateEmployee = {
      first_name: firstName,
      last_name: lastName,
      position: position,
      restaurant_id: restaurantId,
      is_active: isActive,
    };

    updateEmployeeMutation.mutate(updatedFields);
  };

  if (isLoadingEmployee || isLoadingRestaurants || isLoadingRoles) {
    return (
      <div className="container mx-auto p-4 max-w-2xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isErrorEmployee || isErrorRestaurants || isErrorRoles) {
    const errorMessage =
      errorEmployee?.message ||
      errorRestaurants?.message ||
      errorRoles?.message ||
      'Unknown error';
    toast.error('Error loading data', {
      description: errorMessage,
    });
    return (
      <div className="container mx-auto p-4 max-w-2xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Employee Data</h1>
        <p>Could not load employee details or related lists: {errorMessage}</p>
        <Button onClick={() => router.push('/employees')} className="mt-4">
          Back to Employees
        </Button>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="container mx-auto p-4 max-w-2xl text-center">
        <h1 className="text-2xl font-bold mb-4">Employee Not Found</h1>
        <p>The employee you are trying to edit does not exist.</p>
        <Button onClick={() => router.push('/employees')} className="mt-4">
          Back to Employees
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Edit Employee
          </CardTitle>
          <CardDescription className="text-gray-600">
            Update the details for {employee.first_name} {employee.last_name}.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName" className="text-gray-700">
                  First Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="firstName"
                  type="text"
                  placeholder="e.g., Jane"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  disabled={updateEmployeeMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
              <div>
                <Label htmlFor="lastName" className="text-gray-700">
                  Last Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="lastName"
                  type="text"
                  placeholder="e.g., Doe"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  disabled={updateEmployeeMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="restaurant" className="text-gray-700">
                Restaurant <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => setRestaurantId(value)}
                value={restaurantId || ''}
                disabled={
                  updateEmployeeMutation.isPending || isLoadingRestaurants
                }
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select a restaurant" />
                </SelectTrigger>
                <SelectContent>
                  {restaurants?.map((r) => (
                    <SelectItem key={r.id} value={r.id}>
                      {r.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="position" className="text-gray-700">
                Position (Default Role) <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => setPosition(value)}
                value={position || ''}
                disabled={updateEmployeeMutation.isPending || isLoadingRoles}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select a position/role" />
                </SelectTrigger>
                <SelectContent>
                  {roles?.map((r) => (
                    <SelectItem key={r.id} value={r.role_name}>
                      {r.role_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked) => setIsActive(!!checked)}
                disabled={updateEmployeeMutation.isPending}
              />
              <Label htmlFor="isActive" className="text-gray-700">
                Is Active?
              </Label>
            </div>

            <Button
              type="submit"
              disabled={
                updateEmployeeMutation.isPending ||
                !restaurantId ||
                !firstName ||
                !lastName ||
                !position
              }
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {updateEmployeeMutation.isPending
                ? 'Saving Changes...'
                : 'Save Changes'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

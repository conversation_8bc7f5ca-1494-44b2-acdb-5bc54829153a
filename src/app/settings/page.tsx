'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useAppStore } from '@/store/appStore';

// Define types for Restaurant data, including new payroll fields
type Restaurant = Database['public']['Tables']['restaurants']['Row'];
type UpdateRestaurant = Database['public']['Tables']['restaurants']['Update'];

export default function SettingsPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { user, selectedRestaurantId } = useAppStore();

  const [payrollPeriodType, setPayrollPeriodType] = useState<string>('');
  const [payrollStartDay, setPayrollStartDay] = useState<string>('');
  const [language, setLanguage] = useState<string>('English'); // Placeholder for language setting

  // Query to fetch the selected restaurant's details
  const {
    data: selectedRestaurant,
    isLoading: isLoadingRestaurant,
    isError: isErrorRestaurant,
    error: errorRestaurant,
  } = useQuery<Restaurant, Error>({
    queryKey: ['selectedRestaurantSettings', selectedRestaurantId],
    queryFn: async () => {
      if (!selectedRestaurantId) throw new Error('No restaurant selected.');
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .eq('id', selectedRestaurantId)
        .single();
      if (error)
        throw new Error(
          `Failed to fetch restaurant settings: ${error.message}`
        );
      return data;
    },
    enabled: !!selectedRestaurantId, // Only run if a restaurant is selected
    refetchOnWindowFocus: false,
  });

  // Populate form fields when restaurant data is loaded
  useEffect(() => {
    if (selectedRestaurant) {
      setPayrollPeriodType(selectedRestaurant.payroll_period_type || '');
      setPayrollStartDay(selectedRestaurant.payroll_start_day || '');
      // If you had a language setting per restaurant, you'd load it here
    }
  }, [selectedRestaurant]);

  // Mutation to update restaurant settings
  const updateRestaurantSettingsMutation = useMutation({
    mutationFn: async (updatedFields: UpdateRestaurant) => {
      if (!selectedRestaurantId)
        throw new Error('No restaurant selected for update.');
      const { data, error } = await supabase
        .from('restaurants')
        .update(updatedFields)
        .eq('id', selectedRestaurantId)
        .select()
        .single();
      if (error)
        throw new Error(
          `Failed to update restaurant settings: ${error.message}`
        );
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ['selectedRestaurantSettings', selectedRestaurantId],
      });
      queryClient.invalidateQueries({ queryKey: ['restaurants'] }); // Invalidate general restaurants list
      toast.success('Settings Saved!', {
        description: `Restaurant settings for ${data.name} updated successfully.`,
      });
    },
    onError: (error: Error) => {
      toast.error('Error saving settings', {
        description: error.message,
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRestaurantId) {
      toast.warning('No Restaurant Selected', {
        description: 'Please select a restaurant to save settings.',
      });
      return;
    }

    const updatedFields: UpdateRestaurant = {
      payroll_period_type: payrollPeriodType || null,
      payroll_start_day: payrollStartDay || null,
    };

    updateRestaurantSettingsMutation.mutate(updatedFields);
  };

  if (isLoadingRestaurant) {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-1/2 mb-2" />
            <Skeleton className="h-4 w-1/3" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isErrorRestaurant) {
    toast.error('Error loading restaurant settings', {
      description: errorRestaurant.message,
    });
    return (
      <div className="container mx-auto p-4 max-w-4xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Settings</h1>
        <p>Could not load restaurant settings: {errorRestaurant.message}</p>
        <Link href="/" passHref>
          <Button className="mt-4">Go to Dashboard</Button>
        </Link>
      </div>
    );
  }

  if (!selectedRestaurantId) {
    return (
      <div className="container mx-auto p-4 max-w-4xl text-center">
        <Card className="shadow-lg rounded-lg p-8">
          <CardTitle className="text-2xl font-bold text-red-700 mb-4">
            No Restaurant Selected
          </CardTitle>
          <CardDescription className="text-gray-600 mb-6">
            Please select a restaurant from the dropdown in the header to manage
            its settings.
          </CardDescription>
          <Link href="/" passHref>
            <Button>Go to Dashboard</Button>
          </Link>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Application Settings
          </CardTitle>
          <CardDescription className="text-gray-600">
            Manage general application settings and restaurant-specific
            configurations.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-8">
          {/* Signed In User Info */}
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-gray-800">
              Signed In User
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-gray-700">Email:</Label>
                <p className="text-lg text-gray-900 font-medium">
                  {user?.email || 'N/A'}
                </p>
              </div>
              {/* You could add more user details here if available in your user profile table */}
            </div>
          </div>

          {/* Company Info (for selected restaurant) */}
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-gray-800">
              Company Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-gray-700">Restaurant Name:</Label>
                <p className="text-lg text-gray-900 font-medium">
                  {selectedRestaurant?.name || 'N/A'}
                </p>
              </div>
              <div>
                <Label className="text-gray-700">Address:</Label>
                <p className="text-lg text-gray-900">
                  {selectedRestaurant?.address || 'N/A'}
                </p>
              </div>
              <div>
                <Label className="text-gray-700">Phone Number:</Label>
                <p className="text-lg text-gray-900">
                  {selectedRestaurant?.phone_number || 'N/A'}
                </p>
              </div>
            </div>
          </div>

          {/* Payroll Period Settings */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <h3 className="text-xl font-semibold text-gray-800">
              Payroll Period Settings
            </h3>
            <div>
              <Label htmlFor="payrollPeriodType" className="text-gray-700">
                Payroll Period Type
              </Label>
              <Select
                onValueChange={setPayrollPeriodType}
                value={payrollPeriodType}
                disabled={updateRestaurantSettingsMutation.isPending}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select period type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Weekly">Weekly</SelectItem>
                  <SelectItem value="Bi-Weekly">Bi-Weekly</SelectItem>
                  <SelectItem value="Monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="payrollStartDay" className="text-gray-700">
                Payroll Start Day
              </Label>
              <Select
                onValueChange={setPayrollStartDay}
                value={payrollStartDay}
                disabled={updateRestaurantSettingsMutation.isPending}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Select start day" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Monday">Monday</SelectItem>
                  <SelectItem value="Tuesday">Tuesday</SelectItem>
                  <SelectItem value="Wednesday">Wednesday</SelectItem>
                  <SelectItem value="Thursday">Thursday</SelectItem>
                  <SelectItem value="Friday">Friday</SelectItem>
                  <SelectItem value="Saturday">Saturday</SelectItem>
                  <SelectItem value="Sunday">Sunday</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Language Setting - Placeholder for now */}
            <div className="space-y-2">
              <h3 className="text-xl font-semibold text-gray-800">
                Language Settings
              </h3>
              <div>
                <Label htmlFor="language" className="text-gray-700">
                  Application Language
                </Label>
                <Select
                  onValueChange={setLanguage}
                  value={language}
                  disabled={true} // Disable for now as it's a placeholder
                >
                  <SelectTrigger className="w-full mt-1">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="English">English</SelectItem>
                    {/* Add more languages here if supported */}
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500 mt-1">
                  More language options coming soon!
                </p>
              </div>
            </div>

            <Button
              type="submit"
              disabled={updateRestaurantSettingsMutation.isPending}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {updateRestaurantSettingsMutation.isPending
                ? 'Saving...'
                : 'Save Settings'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

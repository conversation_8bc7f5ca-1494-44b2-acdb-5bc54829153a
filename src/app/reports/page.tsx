'use client';

import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useAppStore } from '@/store/appStore';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import Link from 'next/link'; // Import Link

// Define types for TipDistribution data
type TipDistribution = Database['public']['Tables']['tip_distributions']['Row'];

// Extend TipDistribution type to include joined data for display
type TipDistributionWithDetails = TipDistribution & {
  tip_pools: {
    pool_date: string;
    total_tips: number;
    distribution_method: string;
    restaurant_id: string; // Add restaurant_id here as it's selected
    restaurants: {
      // Nested restaurants object under tip_pools
      name: string;
    } | null;
  } | null;
  employees: {
    first_name: string;
    last_name: string;
    position: string; // Employee's general position/role
  } | null;
  // Removed direct 'restaurants' from here as it's now nested under tip_pools
};

export default function ReportsPage() {
  const { selectedRestaurantId } = useAppStore();

  // Query to fetch all tip distributions for the selected restaurant
  const {
    data: distributions,
    isLoading,
    isError,
    error,
  } = useQuery<TipDistributionWithDetails[], Error>({
    queryKey: ['tipDistributionsReport', selectedRestaurantId],
    queryFn: async () => {
      if (!selectedRestaurantId) return [];

      const { data, error } = await supabase
        .from('tip_distributions')
        .select(
          `
          *,
          tip_pools (
            pool_date,
            total_tips,
            distribution_method,
            restaurant_id,
            restaurants (
              name
            )
          ),
          employees (
            first_name,
            last_name,
            position
          )
        `
        )
        // Corrected filter: filter by tip_pools.restaurant_id
        .eq('tip_pools.restaurant_id', selectedRestaurantId)
        .order('distributed_at', { ascending: false }); // Order by most recent distribution first

      if (error) {
        throw new Error(
          `Failed to fetch tip distributions for report: ${error.message}`
        );
      }
      return data;
    },
    enabled: !!selectedRestaurantId, // Only enable query if a restaurant is selected
    refetchOnWindowFocus: false,
  });

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 max-w-6xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">
            Tip Distribution Reports
          </h1>
          <Skeleton className="h-10 w-40 rounded-md" />
        </div>
        <Card className="w-full">
          <CardHeader>
            <Skeleton className="h-6 w-1/2 mb-2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-40 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isError) {
    toast.error('Error loading reports', {
      description: error.message,
    });
    return (
      <div className="container mx-auto p-4 max-w-6xl text-red-600 text-center">
        <h1 className="text-3xl font-bold mb-6">Error Loading Reports</h1>
        <p>There was an error connecting to Supabase or fetching data:</p>
        <p className="font-mono text-sm mt-2">{error.message}</p>
        <p className="mt-4">
          Please ensure your database is running and accessible.
        </p>
      </div>
    );
  }

  if (!selectedRestaurantId) {
    return (
      <div className="container mx-auto p-4 max-w-6xl text-center">
        <Card className="shadow-lg rounded-lg p-8">
          <CardTitle className="text-2xl font-bold text-red-700 mb-4">
            No Restaurant Selected
          </CardTitle>
          <CardDescription className="text-gray-600 mb-6">
            Please select a restaurant from the dropdown in the header to view
            tip distribution reports.
          </CardDescription>
          {/* Changed Button with router.push to Link component */}
          <Link href="/" passHref>
            <Button>Go to Dashboard</Button>
          </Link>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">
          Tip Distribution Reports
        </h1>
      </div>

      {distributions && distributions.length > 0 ? (
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-4 rounded-t-lg">
            <CardTitle className="text-xl font-semibold text-blue-800">
              All Tip Distributions
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Employee</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Restaurant</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead className="text-right">Amount Received</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {distributions.map((dist) => (
                  <TableRow key={dist.id}>
                    <TableCell>
                      {dist.tip_pools?.pool_date
                        ? format(new Date(dist.tip_pools.pool_date), 'PPP')
                        : 'N/A'}
                    </TableCell>
                    <TableCell>
                      {dist.employees
                        ? `${dist.employees.first_name} ${dist.employees.last_name}`
                        : 'N/A'}
                    </TableCell>
                    <TableCell>{dist.employees?.position || 'N/A'}</TableCell>
                    {/* Access restaurant name through tip_pools */}
                    <TableCell>
                      {dist.tip_pools?.restaurants?.name || 'N/A'}
                    </TableCell>
                    <TableCell>
                      {dist.tip_pools?.distribution_method
                        ? dist.tip_pools.distribution_method.replace(/_/g, ' ')
                        : 'N/A'}
                    </TableCell>
                    <TableCell className="text-right">
                      ${dist.amount_distributed.toFixed(2)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        <div className="text-center text-gray-600 p-8 border border-gray-300 rounded-lg bg-gray-50">
          <p className="text-lg font-medium mb-2">
            No tip distributions found for this restaurant.
          </p>
          <p className="text-sm">
            Distribute tips for a tip pool to see results here.
          </p>
        </div>
      )}
    </div>
  );
}

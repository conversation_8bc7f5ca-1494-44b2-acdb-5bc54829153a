'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Label } from '@/components/ui/label'; // Import the Label component
import { Input } from '@/components/ui/input'; // Import the Input component
import { toast } from 'sonner';
import { useAppStore } from '@/store/appStore';
import { calculateHoursWorked, ShiftBreak } from '@/lib/utils/timeCalculations';
import { format } from 'date-fns'; // For better date/time formatting

// Define types for Shift, Employee, Role, and ShiftBreak data
type Shift = Database['public']['Tables']['shifts']['Row'];
type UpdateShift = Database['public']['Tables']['shifts']['Update'];
type Employee = Database['public']['Tables']['employees']['Row'];
type Role = Database['public']['Tables']['roles']['Row'];
type ExistingShiftBreak = Database['public']['Tables']['shift_breaks']['Row'];

// Extend Shift type to include joined employee, role, and breaks data
type ShiftWithDetails = Shift & {
  employees: { first_name: string; last_name: string } | null;
  roles: { role_name: string } | null;
  shift_breaks: ExistingShiftBreak[]; // Array of associated breaks
};

export default function ClockInOutPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { selectedRestaurantId } = useAppStore();

  const [filterDate, setFilterDate] = useState(
    format(new Date(), 'yyyy-MM-dd')
  );

  // Query to fetch shifts for the selected restaurant and date
  const {
    data: shifts,
    isLoading,
    isError,
    error,
  } = useQuery<ShiftWithDetails[], Error>({
    queryKey: ['clockioShifts', selectedRestaurantId, filterDate],
    queryFn: async () => {
      if (!selectedRestaurantId) return [];

      const startOfDay = new Date(filterDate);
      startOfDay.setHours(0, 0, 0, 0); // Start of the selected day
      const endOfDay = new Date(filterDate);
      endOfDay.setHours(23, 59, 59, 999); // End of the selected day

      const { data, error } = await supabase
        .from('shifts')
        .select(
          `
          *,
          employees (
            first_name,
            last_name
          ),
          roles (
            role_name
          ),
          shift_breaks (*)
        `
        )
        .eq('restaurant_id', selectedRestaurantId)
        .gte('planned_start_time', startOfDay.toISOString())
        .lte('planned_start_time', endOfDay.toISOString())
        .order('planned_start_time', { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch shifts: ${error.message}`);
      }
      return data;
    },
    enabled: !!selectedRestaurantId && !!filterDate,
    refetchOnWindowFocus: false,
  });

  // Mutation for clocking in or out
  const updateShiftTimesMutation = useMutation({
    mutationFn: async ({
      shiftId,
      updateData,
    }: {
      shiftId: string;
      updateData: UpdateShift;
    }) => {
      const { data, error } = await supabase
        .from('shifts')
        .update(updateData)
        .eq('id', shiftId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update shift: ${error.message}`);
      }
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ['clockioShifts', selectedRestaurantId, filterDate],
      }); // Refetch shifts for the current day
      queryClient.invalidateQueries({
        queryKey: ['shifts', selectedRestaurantId],
      }); // Also invalidate the main shifts list
      toast('Shift Updated!', {
        description: `Shift for ${data.employee_id} has been ${data.clock_out_time ? 'clocked out' : 'clocked in'}.`,
      });
    },
    onError: (error: Error) => {
      toast.error('Error updating shift', {
        description: error.message,
      });
    },
  });

  const handleClockIn = (shift: ShiftWithDetails) => {
    const now = new Date();
    const updateData: UpdateShift = {
      clock_in_time: now.toISOString(),
    };
    updateShiftTimesMutation.mutate({ shiftId: shift.id, updateData });
  };

  const handleClockOut = (shift: ShiftWithDetails) => {
    const now = new Date();
    if (!shift.clock_in_time) {
      toast.warning('Cannot Clock Out', {
        description: 'Employee has not clocked in yet.',
      });
      return;
    }

    const clockInTime = new Date(shift.clock_in_time);
    if (now.getTime() < clockInTime.getTime()) {
      toast.warning('Invalid Clock Out Time', {
        description: 'Clock out time cannot be before clock in time.',
      });
      return;
    }

    // Recalculate hours worked based on actual clock in/out and breaks
    const actualHoursWorked = calculateHoursWorked(
      clockInTime,
      now,
      shift.shift_breaks.map(
        (b) =>
          ({
            break_start_time: b.break_start_time,
            break_end_time: b.break_end_time,
            is_paid_break: b.is_paid_break,
          }) as ShiftBreak
      ) // Cast to ShiftBreak type
    );

    const updateData: UpdateShift = {
      clock_out_time: now.toISOString(),
      hours_worked:
        actualHoursWorked !== null
          ? parseFloat(actualHoursWorked.toFixed(2))
          : null,
    };
    updateShiftTimesMutation.mutate({ shiftId: shift.id, updateData });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 max-w-6xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Clock In/Out</h1>
          <Skeleton className="h-10 w-40 rounded-md" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} className="w-full">
              <CardHeader>
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    toast.error('Error loading shifts for clock in/out', {
      description: error.message,
    });
    return (
      <div className="container mx-auto p-4 max-w-6xl text-red-600 text-center">
        <h1 className="text-3xl font-bold mb-6">Error Loading Shifts</h1>
        <p>There was an error connecting to Supabase or fetching data:</p>
        <p className="font-mono text-sm mt-2">{error.message}</p>
        <p className="mt-4">
          Please ensure your database is running and accessible.
        </p>
      </div>
    );
  }

  if (!selectedRestaurantId) {
    return (
      <div className="container mx-auto p-4 max-w-6xl text-center">
        <Card className="shadow-lg rounded-lg p-8">
          <CardTitle className="text-2xl font-bold text-red-700 mb-4">
            No Restaurant Selected
          </CardTitle>
          <CardDescription className="text-gray-600 mb-6">
            Please select a restaurant from the dropdown in the header to manage
            clock in/out.
          </CardDescription>
          <Button onClick={() => router.push('/')}>Go to Dashboard</Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 space-y-4 sm:space-y-0 sm:space-x-4">
        <h1 className="text-3xl font-bold text-gray-800">Clock In/Out</h1>
        <div className="flex items-center space-x-2">
          <Label htmlFor="filterDate" className="sr-only">
            Filter Date
          </Label>
          <Input
            id="filterDate"
            type="date"
            value={filterDate}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setFilterDate(e.target.value)
            }
            className="w-full sm:w-auto"
          />
        </div>
      </div>

      {shifts && shifts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {shifts.map((shift) => {
            const isClockedIn = !!shift.clock_in_time && !shift.clock_out_time;
            const isCompleted = !!shift.clock_in_time && !!shift.clock_out_time;

            return (
              <Card
                key={shift.id}
                className="w-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg"
              >
                <CardHeader
                  className={`p-4 rounded-t-lg ${isCompleted ? 'bg-green-50' : isClockedIn ? 'bg-yellow-50' : 'bg-blue-50'}`}
                >
                  <CardTitle className="text-xl font-semibold text-blue-800">
                    {shift.employees?.first_name} {shift.employees?.last_name}{' '}
                    {/* Combined for full name */}
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    {shift.roles?.role_name || 'N/A'}
                  </p>
                </CardHeader>
                <CardContent className="p-4 text-gray-700">
                  <p className="text-sm mb-2">
                    <span className="font-medium">Planned:</span>{' '}
                    {format(new Date(shift.planned_start_time), 'hh:mm a')} -{' '}
                    {format(new Date(shift.planned_end_time), 'hh:mm a')}
                  </p>
                  <p className="text-sm mb-2">
                    <span className="font-medium">Clock In:</span>{' '}
                    {shift.clock_in_time
                      ? format(new Date(shift.clock_in_time), 'hh:mm:ss a')
                      : 'N/A'}
                  </p>
                  <p className="text-sm mb-2">
                    <span className="font-medium">Clock Out:</span>{' '}
                    {shift.clock_out_time
                      ? format(new Date(shift.clock_out_time), 'hh:mm:ss a')
                      : 'N/A'}
                  </p>
                  <p className="text-sm mb-2">
                    <span className="font-medium">Breaks:</span>{' '}
                    {shift.shift_breaks.length}
                    {shift.shift_breaks.length > 0 && (
                      <ul className="list-disc list-inside ml-2 text-xs text-gray-600">
                        {shift.shift_breaks.map((b, idx) => (
                          <li key={idx}>
                            {format(new Date(b.break_start_time), 'hh:mm a')} -{' '}
                            {format(new Date(b.break_end_time), 'hh:mm a')} (
                            {b.is_paid_break ? 'Paid' : 'Unpaid'})
                          </li>
                        ))}
                      </ul>
                    )}
                  </p>
                  <p className="text-sm mt-2 font-semibold">
                    Hours Worked:{' '}
                    {shift.hours_worked !== null
                      ? shift.hours_worked.toFixed(2)
                      : 'N/A'}
                  </p>

                  <div className="mt-4 flex space-x-2">
                    {!isClockedIn && !isCompleted && (
                      <Button
                        onClick={() => handleClockIn(shift)}
                        disabled={updateShiftTimesMutation.isPending}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white rounded-md"
                      >
                        {updateShiftTimesMutation.isPending
                          ? 'Clocking In...'
                          : 'Clock In'}
                      </Button>
                    )}
                    {isClockedIn && (
                      <Button
                        onClick={() => handleClockOut(shift)}
                        disabled={updateShiftTimesMutation.isPending}
                        className="flex-1 bg-red-600 hover:bg-red-700 text-white rounded-md"
                      >
                        {updateShiftTimesMutation.isPending
                          ? 'Clocking Out...'
                          : 'Clock Out'}
                      </Button>
                    )}
                    {isCompleted && (
                      <Button
                        disabled
                        className="flex-1 bg-gray-400 text-white rounded-md cursor-not-allowed"
                      >
                        Completed
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <div className="text-center text-gray-600 p-8 border border-gray-300 rounded-lg bg-gray-50">
          <p className="text-lg font-medium mb-2">
            No shifts found for {format(new Date(filterDate), 'PPP')} for this
            restaurant.
          </p>
          <p className="text-sm">Check another date or schedule new shifts.</p>
        </div>
      )}
    </div>
  );
}

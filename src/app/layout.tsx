'use client'; // This directive is important for using client-side hooks

import './globals.css';
import { Inter } from 'next/font/google';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import AuthGuard from '@/components/auth/AuthGuard'; // Import the AuthGuard component
import Header from '@/components/Header/Header';
import Sidebar from '@/components/Sidebar/Sidebar';

const inter = Inter({ subsets: ['latin'] });

// Create a client
const queryClient = new QueryClient();

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <QueryClientProvider client={queryClient}>
          <AuthGuard>
            <div className="flex flex-col min-h-screen">
              {/* Header is always at the top */}
              <Header />
              <div className="flex flex-1">
                {/* Sidebar on the left, main content on the right */}
                <Sidebar />
                <main className="flex-1 p-6 bg-gray-50 overflow-y-auto">
                  {children}
                </main>
              </div>
            </div>
          </AuthGuard>
          {/* Optional: React Query Devtools for easier debugging in development */}
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </body>
    </html>
  );
}

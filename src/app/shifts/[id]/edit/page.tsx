'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useAppStore } from '@/store/appStore';
import {
  calculateHoursWorked,
  combineDateTime,
  formatDate,
  formatTime,
  ShiftBreak,
} from '@/lib/utils/timeCalculations';
import { Switch } from '@/components/ui/switch';
import { PlusCircle, MinusCircle } from 'lucide-react'; // Icons for add/remove break

// Define types for Shift, Employee, Role, and ShiftBreak data
type Shift = Database['public']['Tables']['shifts']['Row'];
type UpdateShift = Database['public']['Tables']['shifts']['Update'];
type Employee = Database['public']['Tables']['employees']['Row'];
type Role = Database['public']['Tables']['roles']['Row'];
type ExistingShiftBreak = Database['public']['Tables']['shift_breaks']['Row']; // For fetching existing breaks
type NewShiftBreak = Database['public']['Tables']['shift_breaks']['Insert']; // For inserting new breaks

export default function EditShiftPage() {
  const router = useRouter();
  const params = useParams();
  const shiftId = params.id as string;
  const queryClient = useQueryClient();
  const { selectedRestaurantId } = useAppStore();

  const [employeeId, setEmployeeId] = useState<string | null>(null);
  const [roleId, setRoleId] = useState<string | null>(null);
  const [plannedDate, setPlannedDate] = useState('');
  const [plannedStartTime, setPlannedStartTime] = useState('');
  const [plannedEndTime, setPlannedEndTime] = useState('');

  // State for managing multiple breaks
  const [breaks, setBreaks] = useState<ShiftBreak[]>([]); // Using ShiftBreak from timeCalculations
  const [calculatedHours, setCalculatedHours] = useState<number | null>(null);

  // 1. Query to fetch the existing shift data
  const {
    data: shift,
    isLoading: isLoadingShift,
    isError: isErrorShift,
    error: errorShift,
  } = useQuery<Shift, Error>({
    queryKey: ['shift', shiftId],
    queryFn: async () => {
      if (!shiftId) throw new Error('Shift ID is missing.');
      const { data, error } = await supabase
        .from('shifts')
        .select('*')
        .eq('id', shiftId)
        .single();

      if (error) {
        throw new Error(`Failed to fetch shift: ${error.message}`);
      }
      return data;
    },
    enabled: !!shiftId,
    refetchOnWindowFocus: false,
  });

  // 2. Query to fetch existing breaks for this shift
  const {
    data: existingBreaks,
    isLoading: isLoadingExistingBreaks,
    isError: isErrorExistingBreaks,
    error: errorExistingBreaks,
  } = useQuery<ExistingShiftBreak[], Error>({
    queryKey: ['shiftBreaks', shiftId],
    queryFn: async () => {
      if (!shiftId) return [];
      const { data, error } = await supabase
        .from('shift_breaks')
        .select('*')
        .eq('shift_id', shiftId)
        .order('break_start_time', { ascending: true });
      if (error)
        throw new Error(`Failed to fetch shift breaks: ${error.message}`);
      return data;
    },
    enabled: !!shiftId,
    refetchOnWindowFocus: false,
  });

  // 3. Query to fetch employees for the selected restaurant
  const {
    data: employees,
    isLoading: isLoadingEmployees,
    isError: isErrorEmployees,
    error: errorEmployees,
  } = useQuery<Employee[], Error>({
    queryKey: ['employees', selectedRestaurantId],
    queryFn: async () => {
      if (!selectedRestaurantId) return [];
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .eq('restaurant_id', selectedRestaurantId)
        .order('last_name', { ascending: true });
      if (error) throw new Error(`Failed to fetch employees: ${error.message}`);
      return data;
    },
    enabled: !!selectedRestaurantId,
    refetchOnWindowFocus: false,
  });

  // 4. Query to fetch available roles
  const {
    data: roles,
    isLoading: isLoadingRoles,
    isError: isErrorRoles,
    error: errorRoles,
  } = useQuery<Role[], Error>({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('role_name', { ascending: true });
      if (error) throw new Error(`Failed to fetch roles: ${error.message}`);
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // Populate form fields when shift and existing breaks data are loaded
  useEffect(() => {
    if (shift) {
      setEmployeeId(shift.employee_id);
      setRoleId(shift.role_id);
      setPlannedDate(formatDate(shift.planned_start_time));
      setPlannedStartTime(formatTime(shift.planned_start_time));
      setPlannedEndTime(formatTime(shift.planned_end_time));
      setCalculatedHours(shift.hours_worked);
    }
    if (existingBreaks) {
      // Map existing breaks to the ShiftBreak type for local state
      setBreaks(
        existingBreaks.map((b) => ({
          id: b.id, // Keep the ID for updates/deletes
          break_start_time: b.break_start_time,
          break_end_time: b.break_end_time,
          is_paid_break: b.is_paid_break ?? false,
        }))
      );
    }
  }, [shift, existingBreaks]);

  // Effect to calculate hours worked whenever relevant inputs change
  useEffect(() => {
    const shiftStartDateTime = combineDateTime(plannedDate, plannedStartTime);
    const shiftEndDateTime = combineDateTime(plannedDate, plannedEndTime);

    if (shiftStartDateTime && shiftEndDateTime) {
      const hours = calculateHoursWorked(
        shiftStartDateTime,
        shiftEndDateTime,
        breaks, // Pass the array of breaks
        plannedDate
      );
      setCalculatedHours(hours);
    } else {
      setCalculatedHours(null);
    }
  }, [plannedDate, plannedStartTime, plannedEndTime, breaks]);

  // Function to add a new break entry
  const addBreak = () => {
    setBreaks([
      ...breaks,
      {
        break_start_time: '12:00',
        break_end_time: '12:30',
        is_paid_break: false,
      },
    ]);
  };

  // Function to remove a break entry
  const removeBreak = (index: number) => {
    setBreaks(breaks.filter((_, i) => i !== index));
  };

  // Function to handle changes in individual break inputs
  const handleBreakChange = (
    index: number,
    field: 'break_start_time' | 'break_end_time' | 'is_paid_break', // Explicit fields
    value: string | boolean // Union type for value
  ) => {
    const newBreaks = [...breaks];
    if (field === 'is_paid_break') {
      newBreaks[index].is_paid_break = value as boolean;
    } else {
      newBreaks[index][field] = value as string;
    }
    setBreaks(newBreaks);
  };

  // 5. Mutation to update the shift data and its breaks
  const updateShiftMutation = useMutation({
    mutationFn: async ({
      updatedShift,
      updatedBreaks,
    }: {
      updatedShift: UpdateShift;
      updatedBreaks: ShiftBreak[];
    }) => {
      if (!shiftId) throw new Error('Shift ID is missing for update.');

      // 1. Update the main shift record
      const { data: shiftData, error: shiftError } = await supabase
        .from('shifts')
        .update(updatedShift)
        .eq('id', shiftId)
        .select()
        .single();

      if (shiftError) {
        throw new Error(`Failed to update shift: ${shiftError.message}`);
      }

      // 2. Handle breaks: determine new, updated, and deleted breaks
      const currentBreakIds = existingBreaks?.map((b) => b.id) || [];
      const incomingBreakIds = updatedBreaks.map((b) => b.id).filter(Boolean); // Filter out undefined/null IDs

      // Breaks to delete (exist in old but not in new)
      const breaksToDelete = currentBreakIds.filter(
        (id) => !incomingBreakIds.includes(id)
      );
      if (breaksToDelete.length > 0) {
        const { error: deleteError } = await supabase
          .from('shift_breaks')
          .delete()
          .in('id', breaksToDelete);
        if (deleteError)
          console.error('Error deleting old breaks:', deleteError.message);
      }

      // Breaks to insert/update
      for (const b of updatedBreaks) {
        const breakStart = combineDateTime(
          plannedDate,
          formatTime(b.break_start_time)
        );
        const breakEnd = combineDateTime(
          plannedDate,
          formatTime(b.break_end_time)
        );

        if (!breakStart || !breakEnd) {
          console.error(
            'Skipping break due to invalid times during update:',
            b
          );
          continue; // Skip invalid breaks
        }

        const breakPayload: NewShiftBreak = {
          shift_id: shiftId,
          break_start_time: breakStart.toISOString(),
          break_end_time: breakEnd.toISOString(),
          is_paid_break: b.is_paid_break,
        };

        if (b.id) {
          // Update existing break
          const { error: updateBreakError } = await supabase
            .from('shift_breaks')
            .update(breakPayload)
            .eq('id', b.id);
          if (updateBreakError)
            console.error('Error updating break:', updateBreakError.message);
        } else {
          // Insert new break
          const { error: insertBreakError } = await supabase
            .from('shift_breaks')
            .insert(breakPayload);
          if (insertBreakError)
            console.error(
              'Error inserting new break:',
              insertBreakError.message
            );
        }
      }

      return shiftData;
    },
    onSuccess: (data) => {
      // Invalidate queries to refetch latest data
      queryClient.invalidateQueries({ queryKey: ['shift', shiftId] });
      queryClient.invalidateQueries({ queryKey: ['shiftBreaks', shiftId] });
      queryClient.invalidateQueries({
        queryKey: ['shifts', selectedRestaurantId],
      });
      toast('Shift Updated!', {
        description: `Shift for ${data.employee_id} on ${new Date(data.planned_start_time).toLocaleDateString()} has been updated.`,
      });
      router.push('/shifts'); // Redirect back to the shifts list
    },
    onError: (error: Error) => {
      toast.error('Error updating shift', {
        description: error.message,
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRestaurantId) {
      toast.warning('Missing Restaurant', {
        description: 'Please select a restaurant in the header.',
      });
      return;
    }

    if (
      !employeeId ||
      !roleId ||
      !plannedDate ||
      !plannedStartTime ||
      !plannedEndTime
    ) {
      toast.warning('Validation Error', {
        description:
          'Please fill all required shift time fields (Employee, Role, Date, Planned Start/End Times).',
      });
      return;
    }

    const plannedStart = combineDateTime(plannedDate, plannedStartTime);
    const plannedEnd = combineDateTime(plannedDate, plannedEndTime);

    if (!plannedStart || !plannedEnd) {
      toast.error('Date/Time Error', {
        description:
          'Invalid planned start or end time. Please check your inputs.',
      });
      return;
    }

    if (plannedEnd.getTime() < plannedStart.getTime()) {
      toast.warning('Time Error', {
        description: 'Planned end time cannot be before planned start time.',
      });
      return;
    }

    // Validate breaks before submitting
    for (const [index, b] of breaks.entries()) {
      const breakStart = combineDateTime(
        plannedDate,
        formatTime(b.break_start_time)
      );
      const breakEnd = combineDateTime(
        plannedDate,
        formatTime(b.break_end_time)
      );

      if (!breakStart || !breakEnd) {
        toast.error('Break Time Error', {
          description: `Break ${index + 1}: Invalid start or end time.`,
        });
        return;
      }
      if (breakEnd.getTime() < breakStart.getTime()) {
        toast.warning('Break Time Error', {
          description: `Break ${index + 1}: End time cannot be before start time.`,
        });
        return;
      }
      if (
        breakStart.getTime() < plannedStart.getTime() ||
        breakEnd.getTime() > plannedEnd.getTime()
      ) {
        toast.warning('Break Time Error', {
          description: `Break ${index + 1}: Break times must be within the planned shift times.`,
        });
        return;
      }
    }

    if (calculatedHours === null || calculatedHours <= 0) {
      toast.warning('Hours Calculation Error', {
        description:
          'Calculated hours must be greater than 0. Please check your times and breaks.',
      });
      return;
    }

    const updatedShift: UpdateShift = {
      employee_id: employeeId,
      // restaurant_id should not be changed on edit, it's tied to the shift's original creation
      role_id: roleId,
      planned_start_time: plannedStart.toISOString(),
      planned_end_time: plannedEnd.toISOString(),
      hours_worked: calculatedHours,
      // clock_in_time and clock_out_time are NOT updated on this page
    };

    updateShiftMutation.mutate({ updatedShift, updatedBreaks: breaks });
  };

  if (
    isLoadingShift ||
    isLoadingExistingBreaks ||
    isLoadingEmployees ||
    isLoadingRoles
  ) {
    return (
      <div className="container mx-auto p-4 max-w-2xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (
    isErrorShift ||
    isErrorExistingBreaks ||
    isErrorEmployees ||
    isErrorRoles
  ) {
    const errorMessage =
      errorShift?.message ||
      errorExistingBreaks?.message ||
      errorEmployees?.message ||
      errorRoles?.message ||
      'Unknown error';
    toast.error('Error loading data', {
      description: errorMessage,
    });
    return (
      <div className="container mx-auto p-4 max-w-2xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Shift Data</h1>
        <p>Could not load shift details or related lists: {errorMessage}</p>
        <Button onClick={() => router.push('/shifts')} className="mt-4">
          Back to Shifts
        </Button>
      </div>
    );
  }

  if (!shift) {
    return (
      <div className="container mx-auto p-4 max-w-2xl text-center">
        <h1 className="text-2xl font-bold mb-4">Shift Not Found</h1>
        <p>The shift you are trying to edit does not exist.</p>
        <Button onClick={() => router.push('/shifts')} className="mt-4">
          Back to Shifts
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Edit Shift
          </CardTitle>
          <CardDescription className="text-gray-600">
            Update the planned details for this shift.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="employee" className="text-gray-700">
                Employee <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => setEmployeeId(value)}
                value={employeeId || ''}
                disabled={
                  updateShiftMutation.isPending ||
                  isLoadingEmployees ||
                  !employees ||
                  employees.length === 0
                }
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue
                    placeholder={
                      employees?.length === 0
                        ? 'No employees found for this restaurant'
                        : 'Select an employee'
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {employees?.map((emp) => (
                    <SelectItem key={emp.id} value={emp.id}>
                      {emp.first_name} {emp.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="role" className="text-gray-700">
                Role <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => setRoleId(value)}
                value={roleId || ''}
                disabled={
                  updateShiftMutation.isPending ||
                  isLoadingRoles ||
                  !roles ||
                  roles.length === 0
                }
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue
                    placeholder={
                      roles?.length === 0
                        ? 'No roles available'
                        : 'Select a role'
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {roles?.map((r) => (
                    <SelectItem key={r.id} value={r.id}>
                      {r.role_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="plannedDate" className="text-gray-700">
                  Date <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="plannedDate"
                  type="date"
                  value={plannedDate}
                  onChange={(e) => setPlannedDate(e.target.value)}
                  disabled={updateShiftMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
              <div>
                <Label htmlFor="plannedStartTime" className="text-gray-700">
                  Planned Start Time <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="plannedStartTime"
                  type="time"
                  value={plannedStartTime}
                  onChange={(e) => setPlannedStartTime(e.target.value)}
                  disabled={updateShiftMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="plannedEndTime" className="text-gray-700">
                  Planned End Time <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="plannedEndTime"
                  type="time"
                  value={plannedEndTime}
                  onChange={(e) => setPlannedEndTime(e.target.value)}
                  disabled={updateShiftMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
            </div>

            {/* Dynamic Break Times Section */}
            <div className="space-y-4 p-4 border rounded-md bg-gray-50">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-800">Breaks</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addBreak}
                  disabled={updateShiftMutation.isPending}
                >
                  <PlusCircle className="mr-2 h-4 w-4" /> Add Break
                </Button>
              </div>

              {breaks.length === 0 && (
                <p className="text-sm text-gray-600 text-center">
                  No breaks added for this shift.
                </p>
              )}

              {breaks.map((b, index) => (
                <div
                  key={index}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4 border-t pt-4 mt-4 relative"
                >
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeBreak(index)}
                    className="absolute top-0 right-0 -mt-2 -mr-2 p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
                    disabled={updateShiftMutation.isPending}
                  >
                    <MinusCircle className="h-5 w-5" />
                  </Button>
                  <div>
                    <Label
                      htmlFor={`breakStartTime-${index}`}
                      className="text-gray-700"
                    >
                      Break {index + 1} Start
                    </Label>
                    <Input
                      id={`breakStartTime-${index}`}
                      type="time"
                      value={formatTime(b.break_start_time)}
                      onChange={(e) =>
                        handleBreakChange(
                          index,
                          'break_start_time',
                          e.target.value
                        )
                      }
                      disabled={updateShiftMutation.isPending}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor={`breakEndTime-${index}`}
                      className="text-gray-700"
                    >
                      Break {index + 1} End
                    </Label>
                    <Input
                      id={`breakEndTime-${index}`}
                      type="time"
                      value={formatTime(b.break_end_time)}
                      onChange={(e) =>
                        handleBreakChange(
                          index,
                          'break_end_time',
                          e.target.value
                        )
                      }
                      disabled={updateShiftMutation.isPending}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                    />
                  </div>
                  <div className="flex items-center space-x-2 md:col-span-2 mt-2">
                    <Switch
                      id={`isPaidBreak-${index}`}
                      checked={b.is_paid_break}
                      onCheckedChange={(checked) =>
                        handleBreakChange(index, 'is_paid_break', checked)
                      }
                      disabled={updateShiftMutation.isPending}
                    />
                    <Label
                      htmlFor={`isPaidBreak-${index}`}
                      className="text-gray-700"
                    >
                      {b.is_paid_break ? 'Paid Break' : 'Unpaid Break'}
                    </Label>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-right text-lg font-semibold text-gray-800">
              Calculated Planned Hours:{' '}
              {calculatedHours !== null ? calculatedHours.toFixed(2) : 'N/A'}
            </div>

            <Button
              type="submit"
              disabled={
                updateShiftMutation.isPending ||
                !employeeId ||
                !roleId ||
                calculatedHours === null ||
                calculatedHours <= 0
              }
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {updateShiftMutation.isPending
                ? 'Saving Changes...'
                : 'Save Changes'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

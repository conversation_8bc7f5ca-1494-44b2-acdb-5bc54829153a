'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'; // Added useMutation, useQueryClient
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { toast } from 'sonner';
import { useAppStore } from '@/store/appStore'; // Import Zustand store
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'; // Added for delete confirmation
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'; // Added for action menu
import { MoreHorizontal } from 'lucide-react'; // For the Kebab menu icon

// Define types for Shift, Employee, and Role data
type Shift = Database['public']['Tables']['shifts']['Row'];
type Employee = Database['public']['Tables']['employees']['Row'];
type Role = Database['public']['Tables']['roles']['Row'];

// Extend Shift type to include joined employee and role data
type ShiftWithDetails = Shift & {
  break_time_minutes?: number; // Add this field if it exists in the database
  is_paid_break?: boolean; // Add this field if it exists in the database
  employees: { first_name: string; last_name: string } | null;
  roles: { role_name: string } | null;
};

export default function ShiftsListPage() {
  const queryClient = useQueryClient(); // Get the query client instance
  const { selectedRestaurantId } = useAppStore(); // Get the selected restaurant ID from Zustand

  // Use TanStack Query to fetch data from the 'shifts' table
  const {
    data: shifts,
    isLoading,
    isError,
    error,
  } = useQuery<ShiftWithDetails[], Error>({
    queryKey: ['shifts', selectedRestaurantId], // Query key now depends on selectedRestaurantId
    queryFn: async () => {
      if (!selectedRestaurantId) {
        return [];
      }

      // Fetch shifts and join with 'employees' and 'roles'
      const { data, error } = await supabase
        .from('shifts')
        .select(
          `
          *,
          employees (
            first_name,
            last_name
          ),
          roles (
            role_name
          )
        `
        )
        .eq('restaurant_id', selectedRestaurantId) // Filter by selected restaurant
        .order('planned_start_time', { ascending: false }); // Order by most recent shifts first

      if (error) {
        throw new Error(`Failed to fetch shifts: ${error.message}`);
      }
      return data;
    },
    enabled: !!selectedRestaurantId, // Only run this query if a restaurant is selected
    refetchOnWindowFocus: false,
  });

  // Mutation hook for deleting a shift
  const deleteShiftMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase.from('shifts').delete().eq('id', id);
      if (error) {
        throw new Error(`Failed to delete shift: ${error.message}`);
      }
      return id; // Return the ID of the deleted item
    },
    onSuccess: (deletedId) => {
      // Invalidate the 'shifts' query to refetch data and update the UI
      queryClient.invalidateQueries({
        queryKey: ['shifts', selectedRestaurantId],
      });
      toast('Shift Deleted!', {
        description: `Shift with ID ${deletedId.substring(0, 8)}... has been removed.`,
      });
    },
    onError: (error: Error) => {
      toast.error('Error deleting shift', {
        description: error.message,
      });
    },
  });

  const handleDelete = (id: string) => {
    deleteShiftMutation.mutate(id);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 max-w-6xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Shifts</h1>
          <Skeleton className="h-10 w-40 rounded-md" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="w-full">
              <CardHeader>
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    toast.error('Error loading shifts', {
      description: error.message,
    });
    return (
      <div className="container mx-auto p-4 max-w-6xl text-red-600 text-center">
        <h1 className="text-3xl font-bold mb-6">Error Loading Shifts</h1>
        <p>There was an error connecting to Supabase or fetching data:</p>
        <p className="font-mono text-sm mt-2">{error.message}</p>
        <p className="mt-4">
          Please ensure your database is running and accessible.
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Shifts</h1>
        <Link href="/shifts/create" passHref>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-md">
            Add New Shift
          </Button>
        </Link>
      </div>

      {!selectedRestaurantId ? (
        <div className="text-center text-gray-600 p-8 border border-gray-300 rounded-lg bg-gray-50">
          <p className="text-lg font-medium mb-2">No restaurant selected.</p>
          <p className="text-sm">
            Please select a restaurant from the header dropdown to view shifts.
          </p>
        </div>
      ) : shifts && shifts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {shifts.map((shift) => (
            <Card
              key={shift.id}
              className="w-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg"
            >
              <CardHeader className="bg-blue-50 p-4 rounded-t-lg flex flex-row items-center justify-between">
                <CardTitle className="text-xl font-semibold text-blue-800">
                  {shift.employees?.first_name} {shift.employees?.last_name}
                </CardTitle>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link href={`/shifts/${shift.id}/edit`}>Edit</Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <DropdownMenuItem
                          onSelect={(e) => e.preventDefault()}
                          className="text-red-600 focus:text-red-600"
                        >
                          Delete
                        </DropdownMenuItem>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Are you absolutely sure?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. This will permanently
                            delete the shift and remove its data from our
                            servers.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(shift.id)}
                            className="bg-red-600 hover:bg-red-700 text-white"
                            disabled={deleteShiftMutation.isPending}
                          >
                            {deleteShiftMutation.isPending
                              ? 'Deleting...'
                              : 'Continue'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent className="p-4 text-gray-700">
                <p className="text-sm mb-2">
                  <span className="font-medium">Role:</span>{' '}
                  {shift.roles?.role_name || 'N/A'}
                </p>
                <p className="text-sm mb-2">
                  <span className="font-medium">Date:</span>{' '}
                  {new Date(shift.planned_start_time).toLocaleDateString()}
                </p>
                <p className="text-sm mb-2">
                  <span className="font-medium">Planned:</span>{' '}
                  {new Date(shift.planned_start_time).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}{' '}
                  -{' '}
                  {new Date(shift.planned_end_time).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </p>
                <p className="text-sm mb-2">
                  <span className="font-medium">Break:</span>{' '}
                  {shift.break_time_minutes} min (
                  {shift.is_paid_break ? 'Paid' : 'Unpaid'})
                </p>
                <p className="text-sm mt-2">
                  <span className="font-medium">Planned Hours:</span>{' '}
                  {shift.hours_worked !== null
                    ? shift.hours_worked.toFixed(2)
                    : 'N/A'}
                </p>
                <p className="text-sm mt-2 text-gray-500">
                  Clocked In:{' '}
                  {shift.clock_in_time
                    ? new Date(shift.clock_in_time).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })
                    : 'N/A'}
                </p>
                <p className="text-sm text-gray-500">
                  Clocked Out:{' '}
                  {shift.clock_out_time
                    ? new Date(shift.clock_out_time).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })
                    : 'N/A'}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-600 p-8 border border-gray-300 rounded-lg bg-gray-50">
          <p className="text-lg font-medium mb-2">
            No shifts found for this restaurant.
          </p>
          <p className="text-sm">
            Start by adding your first shift using the button above!
          </p>
        </div>
      )}
    </div>
  );
}

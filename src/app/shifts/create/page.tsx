'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useAppStore } from '@/store/appStore';
import {
  calculateHoursWorked,
  combineDateTime,
  formatDate,
  formatTime,
  ShiftBreak,
  calculateTotalBreakMinutes,
} from '@/lib/utils/timeCalculations'; // Import calculateTotalBreakMinutes
import { Switch } from '@/components/ui/switch';
import { PlusCircle, MinusCircle } from 'lucide-react'; // Icons for add/remove break

// Define types for Shift, Employee, and Role data
type NewShift = Database['public']['Tables']['shifts']['Insert'];
type NewShiftBreak = Database['public']['Tables']['shift_breaks']['Insert']; // New type for inserting breaks
type Employee = Database['public']['Tables']['employees']['Row'];
type Role = Database['public']['Tables']['roles']['Row'];

export default function CreateShiftPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { selectedRestaurantId } = useAppStore();

  const [employeeId, setEmployeeId] = useState<string | null>(null);
  const [roleId, setRoleId] = useState<string | null>(null);
  const [plannedDate, setPlannedDate] = useState(formatDate(new Date())); // Default to today
  const [plannedStartTime, setPlannedStartTime] = useState('09:00'); // Default time
  const [plannedEndTime, setPlannedEndTime] = useState('17:00'); // Default time

  // State for managing multiple breaks
  const [breaks, setBreaks] = useState<ShiftBreak[]>([]);
  const [calculatedHours, setCalculatedHours] = useState<number | null>(null);
  const [totalBreakMinutes, setTotalBreakMinutes] = useState<number>(0); // New state for total break minutes

  // Query to fetch employees for the selected restaurant
  const {
    data: employees,
    isLoading: isLoadingEmployees,
    isError: isErrorEmployees,
    error: errorEmployees,
  } = useQuery<Employee[], Error>({
    queryKey: ['employees', selectedRestaurantId],
    queryFn: async () => {
      if (!selectedRestaurantId) return [];
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .eq('restaurant_id', selectedRestaurantId)
        .order('last_name', { ascending: true });
      if (error) throw new Error(`Failed to fetch employees: ${error.message}`);
      return data;
    },
    enabled: !!selectedRestaurantId,
    refetchOnWindowFocus: false,
  });

  // Query to fetch available roles
  const {
    data: roles,
    isLoading: isLoadingRoles,
    isError: isErrorRoles,
    error: errorRoles,
  } = useQuery<Role[], Error>({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('role_name', { ascending: true });
      if (error) throw new Error(`Failed to fetch roles: ${error.message}`);
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // Effect to calculate hours worked and total break minutes whenever relevant inputs change
  useEffect(() => {
    const shiftStartDateTime = combineDateTime(plannedDate, plannedStartTime);
    const shiftEndDateTime = combineDateTime(plannedDate, plannedEndTime);
    console.log('Breaks:', shiftEndDateTime, shiftEndDateTime);
    if (shiftStartDateTime && shiftEndDateTime) {
      const hours = calculateHoursWorked(
        shiftStartDateTime,
        shiftEndDateTime,
        breaks, // Pass the array of breaks
        plannedDate
      );
      setCalculatedHours(hours);

      const totalMins = calculateTotalBreakMinutes(breaks, plannedDate); // Calculate total break minutes
      console.log('Total Break Minutes:', totalMins);
      setTotalBreakMinutes(totalMins);
    } else {
      setCalculatedHours(null);
      setTotalBreakMinutes(0);
    }
  }, [plannedDate, plannedStartTime, plannedEndTime, breaks]);

  // Function to add a new break entry
  const addBreak = () => {
    setBreaks([
      ...breaks,
      {
        break_start_time: '12:00',
        break_end_time: '12:30',
        is_paid_break: false,
      },
    ]);
  };

  // Function to remove a break entry
  const removeBreak = (index: number) => {
    setBreaks(breaks.filter((_, i) => i !== index));
  };

  // Function to handle changes in individual break inputs
  const handleBreakChange = (
    index: number,
    field: 'break_start_time' | 'break_end_time' | 'is_paid_break', // Explicit fields
    value: string | boolean // Union type for value
  ) => {
    const newBreaks = [...breaks];
    if (field === 'is_paid_break') {
      newBreaks[index].is_paid_break = value as boolean;
    } else {
      newBreaks[index][field] = value as string; // Directly store the string from input
    }
    setBreaks(newBreaks);
  };

  // Mutation hook for inserting a new shift and its breaks
  const createShiftMutation = useMutation({
    mutationFn: async ({
      newShift,
      newShiftBreaks,
    }: {
      newShift: NewShift;
      newShiftBreaks: NewShiftBreak[];
    }) => {
      // Start a transaction (Supabase client does not have explicit transactions,
      // so we handle atomicity by trying to clean up on error)
      const { data: shiftData, error: shiftError } = await supabase
        .from('shifts')
        .insert(newShift)
        .select()
        .single();

      if (shiftError) {
        throw new Error(`Failed to create shift: ${shiftError.message}`);
      }

      // If shift created, insert breaks
      if (newShiftBreaks.length > 0) {
        const breaksToInsert = newShiftBreaks.map((b) => ({
          ...b,
          shift_id: shiftData.id,
        }));
        const { error: breaksError } = await supabase
          .from('shift_breaks')
          .insert(breaksToInsert);

        if (breaksError) {
          // If break insertion fails, attempt to delete the created shift to maintain atomicity
          await supabase.from('shifts').delete().eq('id', shiftData.id);
          throw new Error(
            `Failed to create shift breaks: ${breaksError.message}. Shift rolled back.`
          );
        }
      }
      return shiftData;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ['shifts', selectedRestaurantId],
      }); // Invalidate shifts list
      toast('Shift Created!', {
        description: `Shift for ${data.employee_id} on ${new Date(data.planned_start_time).toLocaleDateString()} has been scheduled.`,
      });
      router.push('/shifts'); // Redirect to the shifts list page
    },
    onError: (error: Error) => {
      toast.error('Error creating shift', {
        description: error.message,
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRestaurantId) {
      toast.warning('Missing Restaurant', {
        description:
          'Please select a restaurant in the header before creating a shift.',
      });
      return;
    }

    if (
      !employeeId ||
      !roleId ||
      !plannedDate ||
      !plannedStartTime ||
      !plannedEndTime
    ) {
      toast.warning('Validation Error', {
        description:
          'Please fill all required shift time fields (Employee, Role, Date, Planned Start/End Times).',
      });
      return;
    }

    const plannedStart = combineDateTime(plannedDate, plannedStartTime);
    const plannedEnd = combineDateTime(plannedDate, plannedEndTime);

    if (!plannedStart || !plannedEnd) {
      toast.error('Date/Time Error', {
        description:
          'Invalid planned start or end time. Please check your inputs.',
      });
      return;
    }

    if (plannedEnd.getTime() < plannedStart.getTime()) {
      toast.warning('Time Error', {
        description: 'Planned end time cannot be before planned start time.',
      });
      return;
    }

    // Validate breaks
    const breaksToSave: NewShiftBreak[] = [];
    for (const [index, b] of breaks.entries()) {
      // Use the raw string values from the state for validation before combining to Date objects
      const breakStart = combineDateTime(
        plannedDate,
        b.break_start_time as string
      );
      const breakEnd = combineDateTime(plannedDate, b.break_end_time as string);

      if (!breakStart || !breakEnd) {
        toast.error('Break Time Error', {
          description: `Break ${index + 1}: Invalid start or end time.`,
        });
        return;
      }
      if (breakEnd.getTime() < breakStart.getTime()) {
        toast.warning('Break Time Error', {
          description: `Break ${index + 1}: End time cannot be before start time.`,
        });
        return;
      }
      if (
        breakStart.getTime() < plannedStart.getTime() ||
        breakEnd.getTime() > plannedEnd.getTime()
      ) {
        toast.warning('Break Time Error', {
          description: `Break ${index + 1}: Break times must be within the planned shift times.`,
        });
        return;
      }

      breaksToSave.push({
        shift_id: '', // Will be filled after shift is created
        break_start_time: breakStart.toISOString(),
        break_end_time: breakEnd.toISOString(),
        is_paid_break: b.is_paid_break,
      });
    }

    if (calculatedHours === null || calculatedHours <= 0) {
      toast.warning('Hours Calculation Error', {
        description:
          'Calculated hours must be greater than 0. Please check your times and breaks.',
      });
      return;
    }

    const newShift: NewShift = {
      employee_id: employeeId,
      restaurant_id: selectedRestaurantId,
      role_id: roleId,
      planned_start_time: plannedStart.toISOString(),
      planned_end_time: plannedEnd.toISOString(),
      hours_worked: calculatedHours,
      clock_in_time: null,
      clock_out_time: null,
    };

    createShiftMutation.mutate({ newShift, newShiftBreaks: breaksToSave });
  };

  if (isLoadingEmployees || isLoadingRoles) {
    return (
      <div className="container mx-auto p-4 max-w-2xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isErrorEmployees || isErrorRoles) {
    const errorMessage =
      errorEmployees?.message || errorRoles?.message || 'Unknown error';
    toast.error('Error loading data for shift creation', {
      description: errorMessage,
    });
    return (
      <div className="container mx-auto p-4 max-w-2xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Data</h1>
        <p>
          Could not load necessary lists (employees or roles): {errorMessage}
        </p>
        <Button onClick={() => router.push('/shifts')} className="mt-4">
          Back to Shifts
        </Button>
      </div>
    );
  }

  if (!selectedRestaurantId) {
    return (
      <div className="container mx-auto p-4 max-w-2xl text-center">
        <Card className="shadow-lg rounded-lg p-8">
          <CardTitle className="text-2xl font-bold text-red-700 mb-4">
            No Restaurant Selected
          </CardTitle>
          <CardDescription className="text-gray-600 mb-6">
            Please select a restaurant from the dropdown in the header to
            schedule shifts.
          </CardDescription>
          <Button onClick={() => router.push('/')}>Go to Dashboard</Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Schedule New Shift
          </CardTitle>
          <CardDescription className="text-gray-600">
            Plan a new shift for an employee.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="employee" className="text-gray-700">
                Employee <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => setEmployeeId(value)}
                value={employeeId || ''}
                disabled={
                  createShiftMutation.isPending ||
                  isLoadingEmployees ||
                  !employees ||
                  employees.length === 0
                }
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue
                    placeholder={
                      employees?.length === 0
                        ? 'No employees found for this restaurant'
                        : 'Select an employee'
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {employees?.map((emp) => (
                    <SelectItem key={emp.id} value={emp.id}>
                      {emp.first_name} {emp.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="role" className="text-gray-700">
                Role <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => setRoleId(value)}
                value={roleId || ''}
                disabled={
                  createShiftMutation.isPending ||
                  isLoadingRoles ||
                  !roles ||
                  roles.length === 0
                }
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue
                    placeholder={
                      roles?.length === 0
                        ? 'No roles available'
                        : 'Select a role'
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {roles?.map((r) => (
                    <SelectItem key={r.id} value={r.id}>
                      {r.role_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="plannedDate" className="text-gray-700">
                  Date <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="plannedDate"
                  type="date"
                  value={plannedDate}
                  onChange={(e) => setPlannedDate(e.target.value)}
                  disabled={createShiftMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
              <div>
                <Label htmlFor="plannedStartTime" className="text-gray-700">
                  Planned Start Time <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="plannedStartTime"
                  type="time"
                  value={plannedStartTime}
                  onChange={(e) => setPlannedStartTime(e.target.value)}
                  disabled={createShiftMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="plannedEndTime" className="text-gray-700">
                  Planned End Time <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="plannedEndTime"
                  type="time"
                  value={plannedEndTime}
                  onChange={(e) => setPlannedEndTime(e.target.value)}
                  disabled={createShiftMutation.isPending}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                />
              </div>
            </div>

            {/* Dynamic Break Times Section */}
            <div className="space-y-4 p-4 border rounded-md bg-gray-50">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-800">Breaks</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addBreak}
                  disabled={createShiftMutation.isPending}
                >
                  <PlusCircle className="mr-2 h-4 w-4" /> Add Break
                </Button>
              </div>

              {breaks.length === 0 && (
                <p className="text-sm text-gray-600 text-center">
                  No breaks added for this shift.
                </p>
              )}

              {breaks.map((b, index) => (
                <div
                  key={index}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4 border-t pt-4 mt-4 relative"
                >
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeBreak(index)}
                    className="absolute top-0 right-0 -mt-2 -mr-2 p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
                    disabled={createShiftMutation.isPending}
                  >
                    <MinusCircle className="h-5 w-5" />
                  </Button>
                  <div>
                    <Label
                      htmlFor={`breakStartTime-${index}`}
                      className="text-gray-700"
                    >
                      Break {index + 1} Start
                    </Label>
                    <Input
                      id={`breakStartTime-${index}`}
                      type="time"
                      value={b.break_start_time as string} // Directly use the string from state
                      onChange={(e) =>
                        handleBreakChange(
                          index,
                          'break_start_time',
                          e.target.value
                        )
                      }
                      disabled={createShiftMutation.isPending}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor={`breakEndTime-${index}`}
                      className="text-gray-700"
                    >
                      Break {index + 1} End
                    </Label>
                    <Input
                      id={`breakEndTime-${index}`}
                      type="time"
                      value={b.break_end_time as string} // Directly use the string from state
                      onChange={(e) =>
                        handleBreakChange(
                          index,
                          'break_end_time',
                          e.target.value
                        )
                      }
                      disabled={createShiftMutation.isPending}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                    />
                  </div>
                  <div className="flex items-center space-x-2 md:col-span-2 mt-2">
                    <Switch
                      id={`isPaidBreak-${index}`}
                      checked={b.is_paid_break}
                      onCheckedChange={(checked) =>
                        handleBreakChange(index, 'is_paid_break', checked)
                      }
                      disabled={createShiftMutation.isPending}
                    />
                    <Label
                      htmlFor={`isPaidBreak-${index}`}
                      className="text-gray-700"
                    >
                      {b.is_paid_break ? 'Paid Break' : 'Unpaid Break'}
                    </Label>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-between text-lg font-semibold text-gray-800">
              <span>Total Break Time:</span>
              <span>{totalBreakMinutes} minutes</span>
            </div>
            <div className="text-right text-lg font-semibold text-gray-800">
              Calculated Planned Hours (excluding unpaid breaks):{' '}
              {calculatedHours !== null ? calculatedHours.toFixed(2) : 'N/A'}
            </div>

            <Button
              type="submit"
              disabled={
                createShiftMutation.isPending ||
                !employeeId ||
                !roleId ||
                calculatedHours === null ||
                calculatedHours <= 0
              }
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {createShiftMutation.isPending
                ? 'Scheduling Shift...'
                : 'Schedule Shift'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Skeleton } from '@/components/ui/skeleton';

// Define a type for your restaurant data
type Restaurant = Database['public']['Tables']['restaurants']['Row'];
type UpdateRestaurant = Database['public']['Tables']['restaurants']['Update'];

export default function EditRestaurantPage() {
  const router = useRouter();
  const params = useParams(); // Get URL parameters
  const restaurantId = params.id as string; // Extract the restaurant ID from the URL
  const queryClient = useQueryClient();

  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');

  // 1. Query to fetch the existing restaurant data
  const {
    data: restaurant,
    isLoading,
    isError,
    error,
  } = useQuery<Restaurant, Error>({
    queryKey: ['restaurant', restaurantId], // Unique key for this specific restaurant
    queryFn: async () => {
      if (!restaurantId) throw new Error('Restaurant ID is missing.');
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .eq('id', restaurantId)
        .single(); // Use single() to get a single record

      if (error) {
        throw new Error(`Failed to fetch restaurant: ${error.message}`);
      }
      return data;
    },
    enabled: !!restaurantId, // Only run this query if restaurantId is available
    refetchOnWindowFocus: false,
  });

  // Populate form fields when restaurant data is loaded
  useEffect(() => {
    if (restaurant) {
      setName(restaurant.name);
      setAddress(restaurant.address || '');
      setPhoneNumber(restaurant.phone_number || '');
    }
  }, [restaurant]);

  // 2. Mutation to update the restaurant data
  const updateRestaurantMutation = useMutation({
    mutationFn: async (updatedFields: UpdateRestaurant) => {
      if (!restaurantId)
        throw new Error('Restaurant ID is missing for update.');
      const { data, error } = await supabase
        .from('restaurants')
        .update(updatedFields)
        .eq('id', restaurantId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update restaurant: ${error.message}`);
      }
      return data;
    },
    onSuccess: (data) => {
      // Invalidate both the specific restaurant query and the list query
      queryClient.invalidateQueries({ queryKey: ['restaurant', restaurantId] });
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
      toast('Restaurant Updated!', {
        description: `${data.name} has been successfully updated.`,
      });
      router.push('/restaurants'); // Redirect back to the restaurants list
    },
    onError: (error: Error) => {
      toast.error('Error updating restaurant', {
        description: error.message,
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name) {
      toast.warning('Validation Error', {
        description: 'Restaurant Name is required.',
      });
      return;
    }

    // Prepare the updated fields
    const updatedFields: UpdateRestaurant = {
      name,
      address: address || null,
      phone_number: phoneNumber || null,
    };

    updateRestaurantMutation.mutate(updatedFields);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 max-w-2xl">
        <Card className="shadow-lg rounded-lg">
          <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4 max-w-2xl text-red-600 text-center">
        <h1 className="text-2xl font-bold mb-4">Error Loading Restaurant</h1>
        <p>Could not load restaurant details: {error.message}</p>
        <Button onClick={() => router.push('/restaurants')} className="mt-4">
          Back to Restaurants
        </Button>
      </div>
    );
  }

  if (!restaurant) {
    // This case should ideally be covered by isError, but as a fallback
    return (
      <div className="container mx-auto p-4 max-w-2xl text-center">
        <h1 className="text-2xl font-bold mb-4">Restaurant Not Found</h1>
        <p>The restaurant you are trying to edit does not exist.</p>
        <Button onClick={() => router.push('/restaurants')} className="mt-4">
          Back to Restaurants
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Edit Restaurant
          </CardTitle>
          <CardDescription className="text-gray-600">
            Update the details for {restaurant.name}.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="name" className="text-gray-700">
                Restaurant Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="e.g., The Golden Spoon"
                value={name}
                onChange={(e) => setName(e.target.value)}
                disabled={updateRestaurantMutation.isPending}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>

            <div>
              <Label htmlFor="address" className="text-gray-700">
                Address
              </Label>
              <Textarea
                id="address"
                placeholder="e.g., 123 Main Street, Cityville"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                disabled={updateRestaurantMutation.isPending}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>

            <div>
              <Label htmlFor="phone_number" className="text-gray-700">
                Phone Number
              </Label>
              <Input
                id="phone_number"
                type="tel"
                placeholder="e.g., ************"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                disabled={updateRestaurantMutation.isPending}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>

            <Button
              type="submit"
              disabled={updateRestaurantMutation.isPending}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {updateRestaurantMutation.isPending
                ? 'Updating...'
                : 'Save Changes'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils/supabase/supabase';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

export default function CreateRestaurantPage() {
  const router = useRouter();
  const queryClient = useQueryClient(); // Get the query client instance

  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');

  // Mutation hook for inserting a new restaurant
  const createRestaurantMutation = useMutation({
    mutationFn: async (newRestaurant: {
      name: string;
      address?: string | null;
      phone_number?: string | null;
      owner_id: string | null;
    }) => {
      const { data, error } = await supabase
        .from('restaurants')
        .insert(newRestaurant)
        .select()
        .single();
      if (error) {
        throw new Error(`Failed to create restaurant: ${error.message}`);
      }
      return data;
    },
    onSuccess: (data) => {
      // Invalidate the 'restaurants' query to refetch data on the homepage/list
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
      toast(`${data.name} has been successfully added.`, {
        action: {
          label: 'View',
          onClick: () => router.push('/restaurants'),
        },
      });
      router.push('/restaurants'); // Redirect to the restaurants list page
    },
    onError: () => {
      toast('Error creating restaurant', {
        action: {
          label: 'Retry',
          onClick: () =>
            createRestaurantMutation.mutate({
              name,
              address: address || null,
              phone_number: phoneNumber || null,
              owner_id: null, // Adjust as needed
            }),
        },
      });
    },
  });

  const isLoading = createRestaurantMutation.isPending;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Get the current user's ID to set as owner_id
    const {
      data: { user },
    } = await supabase.auth.getUser();
    const ownerId = user?.id || null; // owner_id can be null for now if not logged in or for testing

    if (!name) {
      toast('Restaurant Name is required.');
      return;
    }

    createRestaurantMutation.mutate({
      name,
      address: address || null, // Ensure empty strings become NULL for database
      phone_number: phoneNumber || null,
      owner_id: ownerId,
    });
  };

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card className="shadow-lg rounded-lg">
        <CardHeader className="bg-blue-50 p-6 rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-blue-800">
            Create New Restaurant
          </CardTitle>
          <CardDescription className="text-gray-600">
            Enter the details for your new restaurant.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="name" className="text-gray-700">
                Restaurant Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="e.g., The Golden Spoon"
                value={name}
                onChange={(e) => setName(e.target.value)}
                disabled={isLoading}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>

            <div>
              <Label htmlFor="address" className="text-gray-700">
                Address
              </Label>
              <Textarea
                id="address"
                placeholder="e.g., 123 Main Street, Cityville"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                disabled={isLoading}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>

            <div>
              <Label htmlFor="phone_number" className="text-gray-700">
                Phone Number
              </Label>
              <Input
                id="phone_number"
                type="tel"
                placeholder="e.g., ************"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                disabled={isLoading}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200 ease-in-out"
            >
              {isLoading ? 'Creating...' : 'Create Restaurant'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

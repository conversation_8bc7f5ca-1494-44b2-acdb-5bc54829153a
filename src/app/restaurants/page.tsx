'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/utils//supabase/supabase';
import { type Database } from '@/lib/utils/supabase/database.types'; // Ensure you've generated types
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { toast } from 'sonner'; // Updated: Import toast from sonner
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react'; // For the Kebab menu icon

// Define a type for your restaurant data for better type safety
type Restaurant = Database['public']['Tables']['restaurants']['Row'];

export default function RestaurantsListPage() {
  const queryClient = useQueryClient(); // Get the query client instance

  // Use TanStack Query to fetch data from the 'restaurants' table
  const {
    data: restaurants,
    isLoading,
    isError,
    error,
  } = useQuery<Restaurant[], Error>({
    queryKey: ['restaurants'], // Unique key for this query
    queryFn: async () => {
      // Fetch data from the 'restaurants' table
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .order('name', { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch restaurants: ${error.message}`);
      }
      return data;
    },
    refetchOnWindowFocus: false, // Prevents unnecessary refetches on window focus
  });

  // Mutation hook for deleting a restaurant
  const deleteRestaurantMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('restaurants')
        .delete()
        .eq('id', id);
      if (error) {
        throw new Error(`Failed to delete restaurant: ${error.message}`);
      }
      return id; // Return the ID of the deleted item
    },
    onSuccess: (deletedId) => {
      // Invalidate the 'restaurants' query to refetch data and update the UI
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
      // Updated: sonner toast usage
      toast('Restaurant Deleted!', {
        description: `Restaurant with ID ${deletedId.substring(0, 8)}... has been removed.`,
      });
    },
    onError: (error: Error) => {
      // Updated: sonner error toast usage
      toast.error('Error deleting restaurant', {
        description: error.message,
      });
    },
  });

  const handleDelete = (id: string) => {
    deleteRestaurantMutation.mutate(id);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 max-w-6xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Restaurants</h1>
          <Skeleton className="h-10 w-40 rounded-md" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="w-full">
              <CardHeader>
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4 max-w-6xl text-red-600 text-center">
        <h1 className="text-3xl font-bold mb-6">Error Loading Restaurants</h1>
        <p>There was an error connecting to Supabase or fetching data:</p>
        <p className="font-mono text-sm mt-2">{error.message}</p>
        <p className="mt-4">
          Please ensure your database is running and accessible.
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Restaurants</h1>
        <Link href="/restaurants/create" passHref>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-md">
            Add New Restaurant
          </Button>
        </Link>
      </div>

      {restaurants && restaurants.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {restaurants.map((restaurant) => (
            <Card
              key={restaurant.id}
              className="w-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg"
            >
              <CardHeader className="bg-blue-50 p-4 rounded-t-lg flex flex-row items-center justify-between">
                <CardTitle className="text-xl font-semibold text-blue-800">
                  {restaurant.name}
                </CardTitle>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link href={`/restaurants/${restaurant.id}/edit`}>
                        Edit
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <DropdownMenuItem
                          onSelect={(e) => e.preventDefault()}
                          className="text-red-600 focus:text-red-600"
                        >
                          Delete
                        </DropdownMenuItem>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Are you absolutely sure?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. This will permanently
                            delete the restaurant and remove its data from our
                            servers.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(restaurant.id)}
                            className="bg-red-600 hover:bg-red-700 text-white"
                            disabled={deleteRestaurantMutation.isPending}
                          >
                            {deleteRestaurantMutation.isPending
                              ? 'Deleting...'
                              : 'Continue'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent className="p-4 text-gray-700">
                <p className="text-sm mb-2">
                  <span className="font-medium">Address:</span>{' '}
                  {restaurant.address || 'N/A'}
                </p>
                <p className="text-sm">
                  <span className="font-medium">Phone:</span>{' '}
                  {restaurant.phone_number || 'N/A'}
                </p>
                <p className="text-sm mt-2 text-gray-500">
                  Added:{' '}
                  {restaurant.created_at
                    ? new Date(restaurant.created_at).toLocaleDateString()
                    : 'N/A'}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-600 p-8 border border-gray-300 rounded-lg bg-gray-50">
          <p className="text-lg font-medium mb-2">No restaurants found.</p>
          <p className="text-sm">
            Start by adding your first restaurant using the button above!
          </p>
        </div>
      )}
    </div>
  );
}

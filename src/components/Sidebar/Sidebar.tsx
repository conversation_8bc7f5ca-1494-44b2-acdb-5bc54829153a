'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils'; // Utility for conditional class names
import { navItems } from '@/constants/constants';

export default function Sidebar() {
  const pathname = usePathname();
  return (
    <aside className="w-64 bg-gray-800 text-white flex flex-col p-4 shadow-lg h-full">
      <nav className="flex-1 space-y-2">
        {navItems.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'flex items-center p-3 rounded-md text-sm font-medium transition-colors duration-200',
              'hover:bg-gray-700 hover:text-white',
              pathname === item.href
                ? 'bg-blue-600 text-white shadow-md'
                : 'text-gray-300'
            )}
          >
            {item.name}
          </Link>
        ))}
      </nav>
    </aside>
  );
}

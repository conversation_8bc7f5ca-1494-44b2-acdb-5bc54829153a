'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { supabase } from '@/lib/utils/supabase/supabase';
import { Session, User } from '@supabase/supabase-js';
import { useAppStore } from '@/store/appStore'; // Import your Zustand store
import { type Database } from '@/lib/utils/supabase/database.types';

// Define types for Restaurant
type Restaurant = Database['public']['Tables']['restaurants']['Row'];

// Define paths that do NOT require authentication
const PUBLIC_PATHS = ['/', '/auth', '/auth/callback'];

export default function AuthGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();

  // Get state and setters from Zustand store
  const {
    user,
    setUser,
    userRestaurants,
    setUserRestaurants,
    selectedRestaurantId,
    setSelectedRestaurantId,
    isAppInitialized,
    setAppInitialized,
  } = useAppStore();

  const [authSessionChecked, setAuthSessionChecked] = useState(false); // Tracks if initial Supabase session check is done

  // Effect 1: Listen for Supabase Auth state changes (login/logout/token refresh)
  useEffect(() => {
    console.log('AuthGuard: Effect 1 - Setting up auth listener.');
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      console.log(
        'AuthGuard: Auth state changed. Event:',
        event,
        'Session:',
        session ? 'exists' : 'null'
      );
      setUser(session?.user || null); // Update user in Zustand
      if (!authSessionChecked) {
        // Mark initial check as done if this is the first time
        setAuthSessionChecked(true);
      }
    });

    // Initial session check on component mount (important for direct loads/refreshes)
    supabase.auth.getSession().then(({ data: { session: initialSession } }) => {
      console.log(
        'AuthGuard: Initial getSession result:',
        initialSession ? 'exists' : 'null'
      );
      setUser(initialSession?.user || null); // Set initial user in Zustand
      setAuthSessionChecked(true); // Mark initial check as done
    });

    return () => {
      console.log('AuthGuard: Cleaning up auth listener.');
      subscription.unsubscribe();
    };
  }, [setUser, authSessionChecked]); // Depend on setUser and authSessionChecked

  // Effect 2: React to user state changes (from Auth or initial session) to fetch restaurants and set app state
  useEffect(() => {
    console.log(
      'AuthGuard: Effect 2 - Reacting to user/authSessionChecked. User:',
      user ? 'exists' : 'null',
      'authSessionChecked:',
      authSessionChecked
    );

    if (!authSessionChecked) {
      console.log(
        'AuthGuard: Effect 2 - Auth session not yet checked. Skipping.'
      );
      return; // Wait until the initial session check is complete
    }

    const initializeAppState = async () => {
      console.log('AuthGuard: Initializing app state...');
      if (user) {
        console.log(
          'AuthGuard: User is logged in. Fetching restaurants for user ID:',
          user.id
        );
        const { data: restaurantsData, error: restaurantsError } =
          await supabase
            .from('restaurants')
            .select('*')
            .eq('owner_id', user.id);

        console.log(
          'AuthGuard: Supabase restaurants fetch result - Data:',
          restaurantsData,
          'Error:',
          restaurantsError
        );

        if (restaurantsError) {
          console.error(
            'AuthGuard: Error fetching user restaurants:',
            restaurantsError.message
          );
          setUserRestaurants([]);
          setSelectedRestaurantId(null);
        } else {
          setUserRestaurants(restaurantsData || []);
          if (restaurantsData && restaurantsData.length > 0) {
            // If user has restaurants, set the first one as selected if none is selected
            if (
              !selectedRestaurantId ||
              !restaurantsData.some((r) => r.id === selectedRestaurantId)
            ) {
              setSelectedRestaurantId(restaurantsData[0].id);
              console.log(
                'AuthGuard: Selected first restaurant:',
                restaurantsData[0].id
              );
            }
          } else {
            // User is logged in but has no restaurants
            setSelectedRestaurantId(null);
            console.log(
              'AuthGuard: User has no restaurants. Redirecting to /restaurants/create'
            );
            if (pathname !== '/restaurants/create') {
              router.push('/restaurants/create');
            }
          }
        }

        const isPublicPath = PUBLIC_PATHS.includes(pathname);
        if (
          isPublicPath &&
          pathname !== '/' &&
          pathname !== '/restaurants/create'
        ) {
          console.log('AuthGuard: Redirecting from public auth path to /');
          router.push('/');
        }
      } else {
        // User is NOT logged in
        console.log('AuthGuard: User is NOT logged in.');
        setUserRestaurants([]);
        setSelectedRestaurantId(null);
        const isPublicPath = PUBLIC_PATHS.includes(pathname);
        if (!isPublicPath) {
          console.log('AuthGuard: Redirecting to /auth');
          router.push('/auth');
        }
      }
      setAppInitialized(true); // Mark app as initialized after all checks
      console.log('AuthGuard: App state initialized. isAppInitialized=true');
    };

    initializeAppState();
  }, [
    user,
    authSessionChecked,
    pathname,
    router,
    setUserRestaurants,
    setSelectedRestaurantId,
    selectedRestaurantId,
    setAppInitialized,
  ]);

  // Show a loading indicator while the app is not initialized
  if (!isAppInitialized) {
    console.log('AuthGuard: Displaying initialization loading screen.');
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <p className="text-gray-700">Initializing application...</p>
      </div>
    );
  }

  // If app is initialized, determine if children should be rendered or if a redirect is pending
  const isPublicPath = PUBLIC_PATHS.includes(pathname);
  console.log(
    'AuthGuard: Checking render condition. isPublicPath:',
    isPublicPath,
    'user:',
    user ? 'exists' : 'null'
  );

  // If on a public path, or if user is authenticated, render children
  if (isPublicPath || user) {
    console.log('AuthGuard: Rendering children.');
    return <>{children}</>;
  }

  // Otherwise, don't render children (AuthGuard logic should have initiated a redirect)
  console.log(
    'AuthGuard: Not rendering children. Redirection handled by AuthGuard logic.'
  );
  return null;
}

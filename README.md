This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

### Supabase Setup

1. Install the Supabase CLI globally:

   ```bash
   npm install -g supabase
   ```

2. Log in to your Supabase account:

   ```bash
   supabase login
   ```

3. Link your project to Supabase:

   ```bash
   supabase link --project-ref YOUR_SUPABASE_PROJECT_REF
   # Replace YOUR_SUPABASE_PROJECT_REF with your project ID from the Supabase URL
   ```

4. Generate TypeScript types for your database schema:

   ```bash
   supabase gen types typescript --schema public > src/lib/utils/supabase/database.types.ts
   ```

## Application Structure

The project is organized as follows:

```
.
├── public/                   # Static assets (e.g., images, icons)
├── src/                      # Source code
│   ├── app/                  # Next.js app directory
│   │   ├── favicon.ico       # Favicon for the app
│   │   ├── globals.css       # Global CSS styles
│   │   ├── layout.tsx        # Root layout component
│   │   ├── page.tsx          # Main page component
│   │   └── auth/             # Authentication-related pages
│   │       └── page.tsx      # Authentication page
│   ├── components/           # Reusable UI components
│   │   ├── auth/             # Authentication-related components
│   │   │   └── AuthGuard.tsx # Component for guarding routes
│   │   └── ui/               # General UI components
│   │       ├── button.tsx    # Button component
│   │       ├── card.tsx      # Card component
│   │       ├── input.tsx     # Input component
│   │       └── skeleton.tsx  # Skeleton loader component
│   ├── lib/                  # Utility functions and libraries
│   │   ├── utils.ts          # General utility functions
│   │   └── utils/            # Nested utilities
│   │       └── supabase/     # Supabase-related utilities
│   │           ├── database.types.ts # Supabase database types
│   │           └── supabase.ts       # Supabase client setup
│   └── store/                # State management
│       └── appStore.ts       # Application store
├── supabase/                 # Supabase configuration and migrations
├── package.json              # Project dependencies and scripts
└── README.md                 # Project documentation
```

## Tech Stack

This application is built using the following technologies:

- **Next.js**: A React framework for building server-side rendered and static web applications.
- **TypeScript**: A strongly typed programming language that builds on JavaScript.
- **Supabase**: A backend-as-a-service providing database, authentication, and storage solutions.
- **TanStack Query**: A powerful data-fetching and server-state management library.
- **Zustand**: A small, fast, and scalable state-management solution for client-side state.
- **Tailwind CSS**: A utility-first CSS framework for styling.
- **Shadcn/UI**: A collection of accessible and customizable UI components.
- **React**: A JavaScript library for building user interfaces.
- **Vercel**: A platform for deploying and hosting Next.js applications.
- **ESLint**: A tool for identifying and fixing code quality issues.
- **PostCSS**: A tool for transforming CSS with JavaScript plugins.

These technologies work together to provide a robust, scalable, and maintainable application.
